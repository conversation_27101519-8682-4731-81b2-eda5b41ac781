/**
 * @file example_gif_usage.cpp
 * @brief GIF动画使用示例
 */

#include "my_animation.h"
#include "display/gif_display.h"
#include "display/lcd_display.h"
#include <cstring>

// 假设您已经转换了GIF文件并生成了以下声明
// 这些应该在您的.h文件中声明
extern const lv_image_dsc_t loading_frame_001;
extern const lv_image_dsc_t loading_frame_002;
extern const lv_image_dsc_t loading_frame_003;
extern const lv_image_dsc_t loading_frame_004;
extern const lv_image_dsc_t loading_frame_005;
extern const lv_image_dsc_t loading_frame_006;
extern const lv_image_dsc_t loading_frame_007;
extern const lv_image_dsc_t loading_frame_008;
extern const lv_image_dsc_t loading_frame_009;
extern const lv_image_dsc_t loading_frame_010;
extern const lv_image_dsc_t loading_frame_011;

// 创建帧数组
const lv_image_dsc_t* loading_frames[] = {
    &loading_frame_001,
    &loading_frame_002,
    &loading_frame_003,
    &loading_frame_004,
    &loading_frame_005,
    &loading_frame_006,
    &loading_frame_007,
    &loading_frame_008,
    &loading_frame_009,
    &loading_frame_010,
    &loading_frame_011
};

class MyCustomDisplay : public RgbLcdDisplay {
private:
    GifDisplay* loading_gif_;
    GifDisplay* emotion_gif_;

public:
    MyCustomDisplay(esp_lcd_panel_io_handle_t panel_io, 
                   esp_lcd_panel_handle_t panel, 
                   int width, int height,
                   int offset_x, int offset_y, 
                   bool mirror_x, bool mirror_y, 
                   bool swap_xy, 
                   DisplayFonts fonts)
        : RgbLcdDisplay(panel_io, panel, width, height, offset_x, offset_y, 
                    mirror_x, mirror_y, swap_xy, fonts)
        , loading_gif_(nullptr)
        , emotion_gif_(nullptr)
    {
        SetupGifAnimations();
    }
    
    ~MyCustomDisplay() {
        if (loading_gif_) {
            delete loading_gif_;
        }
        if (emotion_gif_) {
            delete emotion_gif_;
        }
    }

private:
    void SetupGifAnimations() {
        DisplayLockGuard lock(this);
        
        // 创建加载动画GIF
        loading_gif_ = new GifDisplay(GetScreen());
        if (loading_gif_) {
            loading_gif_->LoadGif(loading_frames, 11, 200);  // 5帧，每帧200ms
            loading_gif_->SetPosition(50, 50, 100, 100);
            // 默认不播放，需要时再启动
        }
        
        // 可以创建更多GIF动画...
    }
    Display* GetScreen() { return nullptr; }

public:
    /**
     * @brief 显示加载动画
     */
    void ShowLoadingAnimation() {
        if (loading_gif_) {
            loading_gif_->StartAnimation(true);  // 循环播放
        }
    }
    
    /**
     * @brief 隐藏加载动画
     */
    void HideLoadingAnimation() {
        if (loading_gif_) {
            loading_gif_->StopAnimation();
        }
    }
    
    /**
     * @brief 设置表情动画
     * @param emotion 表情名称
     */
    virtual void SetEmotion(const char* emotion) override {
        // 根据表情名称选择不同的GIF动画
        if (strcmp(emotion, "loading") == 0) {
            ShowLoadingAnimation();
        } else if (strcmp(emotion, "idle") == 0) {
            HideLoadingAnimation();
        }
        // 可以添加更多表情...
    }
};

// 使用示例函数
void example_usage() {
    // 假设您已经初始化了LCD面板
    esp_lcd_panel_io_handle_t panel_io = nullptr;  // 您的面板IO
    esp_lcd_panel_handle_t panel = nullptr;        // 您的面板句柄
    
    // 创建自定义显示对象
    MyCustomDisplay* display = new MyCustomDisplay(
        panel_io, panel, 
        240, 240,  // 宽度，高度
        0, 0,      // 偏移
        false, false, false,  // 镜像和交换设置
        {}         // 字体配置
    );
    
    // 显示加载动画
    display->ShowLoadingAnimation();
    
    // 模拟一些工作
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    // 隐藏加载动画
    display->HideLoadingAnimation();
    
    // 清理
    delete display;
}

// 更简单的使用方式
void create_my_animation_animation(Display* display) {
    // 直接使用便捷函数创建GIF动画
    GifDisplay* gif = CreateGifAnimation(
        display,            // 显示对象
        loading_frames,     // 帧数组
        11,                 // 帧数
        100, 100,           // X, Y位置
        80, 80,             // 宽度，高度
        true                // 循环播放
    );
    
    // GIF会自动开始播放
    
    // 30秒后停止
    /*vTaskDelay(pdMS_TO_TICKS(30000));
    gif->StopAnimation();
    
    // 清理
    delete gif;*/
}
