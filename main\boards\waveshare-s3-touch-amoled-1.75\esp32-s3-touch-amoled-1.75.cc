#include "wifi_board.h"
#include "display/lcd_display.h"
#include "esp_lcd_sh8601.h"
#include "font_awesome_symbols.h"

#include "codecs/box_audio_codec.h"
#include "application.h"
#include "button.h"
#include "led/single_led.h"
#include "mcp_server.h"
#include "config.h"
#include "power_save_timer.h"
#include "i2c_device.h"
#include <wifi_station.h>

#include <esp_log.h>
#include <esp_lcd_panel_vendor.h>
#include <driver/i2c_master.h>
#include <driver/spi_master.h>
#include "settings.h"

#include <esp_lcd_touch_cst820.h>
// #include <esp_lcd_touch_cst9217.h>
#include <esp_lvgl_port.h>
#include <lvgl.h>

#include "esp_chip_info.h"
#include "esp_flash.h"
#include "display/touch_gesture_handler.h"

#ifdef CONFIG_BT_ENABLED
#include "bluetooth/blufi_manager.h"
#endif
#include "storage/sd_card_manager.h"
#include "pmic.h"
#include "power_manager.h"
#include "pwr_manager.h"
#include "assets/lang_config.h"

#define TAG "TouchAMOLED-1.75"

LV_FONT_DECLARE(font_puhui_30_4);
LV_FONT_DECLARE(font_awesome_30_4);

#define LCD_OPCODE_WRITE_CMD (0x02ULL)
#define LCD_OPCODE_READ_CMD (0x03ULL)
#define LCD_OPCODE_WRITE_COLOR (0x32ULL)

static const sh8601_lcd_init_cmd_t vendor_specific_init[] = {
    // set display to qspi mode
    {0xFE, (uint8_t[]){0x20}, 1, 0},
    {0x19, (uint8_t[]){0x10}, 1, 0},
    {0x1C, (uint8_t[]){0xA0}, 1, 0},

    {0xFE, (uint8_t[]){0x00}, 1, 0},
    {0xC4, (uint8_t[]){0x80}, 1, 0},
    {0x3A, (uint8_t[]){0x55}, 1, 0},
    {0x35, (uint8_t[]){0x00}, 1, 0},
    {0x53, (uint8_t[]){0x20}, 1, 0},
    {0x51, (uint8_t[]){0xFF}, 1, 0},
    {0x63, (uint8_t[]){0xFF}, 1, 0},
    {0x2A, (uint8_t[]){0x00, 0x06, 0x01, 0xD7}, 4, 0},
    {0x2B, (uint8_t[]){0x00, 0x00, 0x01, 0xD1}, 4, 600},
    {0x11, NULL, 0, 600},
    {0x29, NULL, 0, 0},
};

// 在waveshare_amoled_1_75类之前添加新的显示类
class CustomLcdDisplay : public SpiLcdDisplay {
public:
    static void rounder_event_cb(lv_event_t* e) {
        lv_area_t* area = (lv_area_t* )lv_event_get_param(e);
        uint16_t x1 = area->x1;
        uint16_t x2 = area->x2;

        uint16_t y1 = area->y1;
        uint16_t y2 = area->y2;

        // round the start of coordinate down to the nearest 2M number
        area->x1 = (x1 >> 1) << 1;
        area->y1 = (y1 >> 1) << 1;
        // round the end of coordinate up to the nearest 2N+1 number
        area->x2 = ((x2 >> 1) << 1) + 1;
        area->y2 = ((y2 >> 1) << 1) + 1;
    }

    CustomLcdDisplay(esp_lcd_panel_io_handle_t io_handle,
                     esp_lcd_panel_handle_t panel_handle,
                     int width,
                     int height,
                     int offset_x,
                     int offset_y,
                     bool mirror_x,
                     bool mirror_y,
                     bool swap_xy)
        : SpiLcdDisplay(io_handle, panel_handle,
                        width, height, offset_x, offset_y, mirror_x, mirror_y, swap_xy,
                        {
                            .text_font = &font_puhui_30_4,
                            .icon_font = &font_awesome_30_4,
#if CONFIG_USE_WECHAT_MESSAGE_STYLE
                            .emoji_font = font_emoji_32_init(),
#else
                            .emoji_font = font_emoji_64_init(),
#endif
                        })
    {
        DisplayLockGuard lock(this);
        lv_obj_set_size(status_bar_, LV_HOR_RES, fonts_.text_font->line_height * 2 + 10);
        lv_obj_set_style_layout(status_bar_, LV_LAYOUT_NONE, 0);
        lv_obj_set_style_pad_top(status_bar_, 10, 0);
        lv_obj_set_style_pad_bottom(status_bar_, 1, 0);

        // 针对圆形屏幕调整位置
        //      network  battery  mute     //
        //               status            //
        lv_obj_align(battery_label_, LV_ALIGN_TOP_MID, -2.5*fonts_.icon_font->line_height, 0);
        lv_obj_align(network_label_, LV_ALIGN_TOP_MID, -0.5*fonts_.icon_font->line_height, 0);
        lv_obj_align(mute_label_, LV_ALIGN_TOP_MID, 1.5*fonts_.icon_font->line_height, 0);
        
        lv_obj_align(status_label_, LV_ALIGN_BOTTOM_MID, 0, 0);
        lv_obj_set_flex_grow(status_label_, 0);
        lv_obj_set_width(status_label_, LV_HOR_RES * 0.75);
        lv_label_set_long_mode(status_label_, LV_LABEL_LONG_SCROLL_CIRCULAR);

        lv_obj_align(notification_label_, LV_ALIGN_BOTTOM_MID, 0, 0);
        lv_obj_set_width(notification_label_, LV_HOR_RES * 0.75);
        lv_label_set_long_mode(notification_label_, LV_LABEL_LONG_SCROLL_CIRCULAR);

        lv_obj_align(low_battery_popup_, LV_ALIGN_BOTTOM_MID, 0, -20);
        lv_obj_set_style_bg_color(low_battery_popup_, lv_color_hex(0xFF0000), 0);
        lv_obj_set_width(low_battery_label_, LV_HOR_RES * 0.75);
        lv_label_set_long_mode(low_battery_label_, LV_LABEL_LONG_SCROLL_CIRCULAR);
        
        lv_display_add_event_cb(display_, rounder_event_cb, LV_EVENT_INVALIDATE_AREA, NULL);
    }

    void StartMultiWin() {
        // 初始化App管理器 (借鉴esp-brookesia)
        InitializeAppManager();
    }
};

class CustomBacklight : public Backlight {
public:
    CustomBacklight(esp_lcd_panel_io_handle_t panel_io) : Backlight(), panel_io_(panel_io) {}

protected:
    esp_lcd_panel_io_handle_t panel_io_;

    virtual void SetBrightnessImpl(uint8_t brightness) override {
        auto display = Board::GetInstance().GetDisplay();
        DisplayLockGuard lock(display);
        uint8_t data[1] = {((uint8_t)((255*  brightness) / 100))};
        int lcd_cmd = 0x51;
        lcd_cmd &= 0xff;
        lcd_cmd <<= 8;
        lcd_cmd |= LCD_OPCODE_WRITE_CMD << 24;
        esp_lcd_panel_io_tx_param(panel_io_, lcd_cmd, &data, sizeof(data));
    }
};

class WaveshareEsp32s3TouchAMOLED1inch75 : public WifiBoard {
private:
    i2c_master_bus_handle_t i2c_bus_;
    Pmic* pmic_ = nullptr;
    Button boot_button_;
    CustomLcdDisplay* display_;
    CustomBacklight* backlight_;
    PowerSaveTimer* power_save_timer_ = nullptr;               // 省电管理
	lv_indev_t* indev_ = nullptr;
    PowerManagement* power_detect_ = nullptr;        // 电池电量检测

    /**
     * 电源按键检测
     */
    PwrBtnManagement* pwr_btn_management_ = nullptr;

    /**
     * 触摸手势处理器
     */
    TouchGestureHandler* touch_gesture_handler_ = nullptr;

    /**
     * SD卡管理
     */
    SDCardManager* sd_card_manager_ = nullptr;
    
    void InitializePowerSaveTimer() {
        power_save_timer_ = new PowerSaveTimer(-1, 60, 300); //300
        power_save_timer_->OnEnterSleepMode([this]() {
            GetDisplay()->SetPowerSaveMode(true);
            GetBacklight()->SetBrightness(20);
        });
        power_save_timer_->OnExitSleepMode([this]() {
            GetDisplay()->SetPowerSaveMode(false);
            GetBacklight()->RestoreBrightness();
        });
        power_save_timer_->OnShutdownRequest([this](){ 
            pmic_->PowerOff(); });
        power_save_timer_->SetEnabled(true);
    }

    void InitializeCodecI2c() {
        // Initialize I2C peripheral
        i2c_master_bus_config_t i2c_bus_cfg = {
            .i2c_port = I2C_NUM_0,
            .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
            .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
            .clk_source = I2C_CLK_SRC_DEFAULT,
            .flags = {
                .enable_internal_pullup = 1,
            },
        };
        ESP_ERROR_CHECK(i2c_new_master_bus(&i2c_bus_cfg, &i2c_bus_));
    }

    void InitializePmic() {
        ESP_LOGI(TAG, "Init pmic");
        pmic_ = new Pmic(CHARGING_STATUS_PIN);
    }

    void InitializeSpi() {
        spi_bus_config_t buscfg = {};
        buscfg.sclk_io_num = EXAMPLE_PIN_NUM_LCD_PCLK;
        buscfg.data0_io_num = EXAMPLE_PIN_NUM_LCD_DATA0;
        buscfg.data1_io_num = EXAMPLE_PIN_NUM_LCD_DATA1;
        buscfg.data2_io_num = EXAMPLE_PIN_NUM_LCD_DATA2;
        buscfg.data3_io_num = EXAMPLE_PIN_NUM_LCD_DATA3;
        buscfg.max_transfer_sz = DISPLAY_WIDTH*  DISPLAY_HEIGHT*  sizeof(uint16_t);
        buscfg.flags = SPICOMMON_BUSFLAG_QUAD;
        ESP_ERROR_CHECK(spi_bus_initialize(SPI2_HOST, &buscfg, SPI_DMA_CH_AUTO));
    }

    void InitializeSH8601Display() {
        esp_lcd_panel_io_handle_t panel_io = nullptr;
        esp_lcd_panel_handle_t panel = nullptr;

        // 液晶屏控制IO初始化
        ESP_LOGD(TAG, "Install panel IO");
        esp_lcd_panel_io_spi_config_t io_config = SH8601_PANEL_IO_QSPI_CONFIG(
            EXAMPLE_PIN_NUM_LCD_CS,
            nullptr,
            nullptr);
        ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi(SPI2_HOST, &io_config, &panel_io));

        // 初始化液晶屏驱动芯片
        ESP_LOGD(TAG, "Install LCD driver");
        const sh8601_vendor_config_t vendor_config = {
            .init_cmds = &vendor_specific_init[0],
            .init_cmds_size = sizeof(vendor_specific_init) / sizeof(sh8601_lcd_init_cmd_t),
            .flags = {
                .use_qspi_interface = 1,
            }};

        esp_lcd_panel_dev_config_t panel_config = {};
        panel_config.reset_gpio_num = EXAMPLE_PIN_NUM_LCD_RST;
        panel_config.rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB;
        panel_config.bits_per_pixel = 16;
        panel_config.vendor_config = (void* )&vendor_config;
        ESP_ERROR_CHECK(esp_lcd_new_panel_sh8601(panel_io, &panel_config, &panel));
        esp_lcd_panel_set_gap(panel, 0x06, 0);
        esp_lcd_panel_reset(panel);
        esp_lcd_panel_init(panel);
        esp_lcd_panel_invert_color(panel, false);
        esp_lcd_panel_mirror(panel, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y);
        esp_lcd_panel_disp_on_off(panel, true);
        display_ = new CustomLcdDisplay(panel_io, panel,
                                        DISPLAY_WIDTH, DISPLAY_HEIGHT, DISPLAY_OFFSET_X, DISPLAY_OFFSET_Y, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y, DISPLAY_SWAP_XY);
        backlight_ = new CustomBacklight(panel_io);
        backlight_->RestoreBrightness();

        if (!IsInWifiConfigMode()) {
            display_->StartMultiWin();
        }
    }

    void InitializeTouch() {
        esp_lcd_touch_handle_t tp;
        esp_lcd_touch_config_t tp_cfg = {
            .x_max = DISPLAY_WIDTH - 1,
            .y_max = DISPLAY_HEIGHT - 1,
            .rst_gpio_num = GPIO_NUM_40,
            .int_gpio_num = GPIO_NUM_NC,
            .levels = {
                .reset = 0,
                .interrupt = 0,
            },
            .flags = {
                .swap_xy = 0,
                .mirror_x = 0,
                .mirror_y = 0,
            },
        };
        esp_lcd_panel_io_handle_t tp_io_handle = NULL;
        esp_lcd_panel_io_i2c_config_t tp_io_config = ESP_LCD_TOUCH_IO_I2C_CST820_CONFIG();
        tp_io_config.scl_speed_hz = 400*  1000;
        ESP_ERROR_CHECK(esp_lcd_new_panel_io_i2c(i2c_bus_, &tp_io_config, &tp_io_handle));
        ESP_LOGI(TAG, "Initialize touch controller");
        ESP_ERROR_CHECK(esp_lcd_touch_new_i2c_cst820(tp_io_handle, &tp_cfg, &tp));
        const lvgl_port_touch_cfg_t touch_cfg = {
            .disp = lv_display_get_default(),
            .handle = tp,
        };
        indev_ = lvgl_port_add_touch(&touch_cfg);
        ESP_LOGI(TAG, "Touch panel initialized successfully");
    }

    void InitializeTouchGestures() {
        ESP_LOGI(TAG, "Initializing touch gestures");

        // 创建触摸手势处理器
        touch_gesture_handler_ = new TouchGestureHandler(indev_);

        // 设置手势参数
        touch_gesture_handler_->SetSwipeThreshold(80);  // 滑动阈值80像素
        touch_gesture_handler_->SetLongPressTime(800);  // 长按800毫秒

        // 设置手势回调
        touch_gesture_handler_->OnSwipeLeft([this]() {
            // 唤醒电源管理
            WakeUp();

            // 向左滑动 - 切换到下一个窗口
            ESP_LOGI(TAG, "Swipe left detected - switching to next window");
            auto display = GetDisplay();
            if (display) {
                display->HandleSwipeLeft();
            }
        });
        touch_gesture_handler_->OnSwipeRight([this]() {
            // 唤醒电源管理
            WakeUp();

            // 向右滑动 - 切换到上一个窗口
            ESP_LOGI(TAG, "Swipe right detected - switching to previous window");
            auto display = GetDisplay();
            if (display) {
                display->HandleSwipeRight();
            }
        });
        touch_gesture_handler_->OnSwipeUp([this]() {
            // 唤醒电源管理
            WakeUp();

            // 向上滑动
            ESP_LOGI(TAG, "Swipe up detected - volume up");
            auto display = GetDisplay();
            // 执行音量控制
            auto codec = GetAudioCodec();
            if (codec) {
                int volume = codec->output_volume() + 10;
                if (volume > 100) volume = 100;
                codec->SetOutputVolume(volume);

                char msg[64];
                snprintf(msg, sizeof(msg), "音量: %d%%", volume);
                display->SetChatMessage("system", msg);
            }
        });
        touch_gesture_handler_->OnSwipeDown([this]() {
            // 唤醒电源管理
            WakeUp();

            // 向下滑动
            ESP_LOGI(TAG, "Swipe down detected - volume down");
            auto display = GetDisplay();
            // 执行音量控制
            auto codec = GetAudioCodec();
            if (codec) {
                int volume = codec->output_volume() - 10;
                if (volume < 0) volume = 0;
                codec->SetOutputVolume(volume);

                char msg[64];
                snprintf(msg, sizeof(msg), "音量: %d%%", volume);
                display->SetChatMessage("system", msg);
            }
        });
        touch_gesture_handler_->OnTap([this]() {
            // 唤醒电源管理
            WakeUp();

            // 点击 - 传递给当前窗口处理
            ESP_LOGI(TAG, "Tap detected");
            auto display = GetDisplay();
            if (display) {
                display->HandleTap();
            }
        });
        touch_gesture_handler_->OnLongPress([this]() {
            // 唤醒电源管理
            WakeUp();

            // 长按 - 传递给当前窗口处理
            ESP_LOGI(TAG, "Long press detected");
            auto display = GetDisplay();
            if (display) {
                display->HandleLongPress();
            }

            ShowSystemInfo();

            if (sd_card_manager_ && sd_card_manager_->IsMounted()) {
                // SD卡已挂载，显示SD卡信息
                SDCardUtils::ShowSDCardInfo(*sd_card_manager_);
            } else {
                // SD卡未挂载，显示错误信息
                ESP_LOGW(TAG, "SD card not mounted");
            }
        });
        touch_gesture_handler_->Enabled(true);

        ESP_LOGI(TAG, "Touch gestures initialized");
    }

    void InitializeButtons() {
        // 单击：原有功能保持不变
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !IsWifiConnected()) {
                ResetWifiConfiguration();
            }
            app.ToggleChatState();
        });

        // 长按：自定义功能 - 重置WiFi配置
        boot_button_.OnLongPress([this]() {
            ESP_LOGI("BootButton", "Long press detected - resetting WiFi configuration");
            ResetWifiConfiguration();
        });

        // 按下：自定义功能 - 开始语音识别
        boot_button_.OnPressDown([this]() {
            ESP_LOGI("BootButton", "Press down - Starting voice recognition");
            WakeUp(); // 唤醒设备

            // 显示语音识别开始提示
            auto display = GetDisplay();
            if (display) {
                display->SetChatMessage("system", "🎤 长按说话中...\n松开结束录音");
            }

            auto& app = Application::GetInstance();
            app.StartListening();
        });

        // 松开：自定义功能 - 停止语音识别
        boot_button_.OnPressUp([this]() {
            ESP_LOGI("BootButton", "Press up - Stopping voice recognition");

            // 显示语音识别结束提示
            auto display = GetDisplay();
            if (display) {
                display->SetChatMessage("system", "🎤 录音结束\n正在识别...");
            }

            auto& app = Application::GetInstance();
            app.StopListening();
        });

#if CONFIG_USE_DEVICE_AEC
        boot_button_.OnDoubleClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateIdle) {
                app.SetAecMode(app.GetAecMode() == kAecOff ? kAecOnDeviceSide : kAecOff);
            }
        });
#endif

        // 三击：自定义功能 - 重启系统
        boot_button_.OnMultipleClick([this]() {
            ESP_LOGI("BootButton", "Triple click - restarting system");
            GetDisplay()->SetChatMessage("system", "系统重启中...");
            vTaskDelay(pdMS_TO_TICKS(1000));
            esp_restart();
        }, 3);
    }

    void InitializePwrBtn() {
        pwr_btn_management_ = new PwrBtnManagement(pmic_, 500);
        pwr_btn_management_->OnShortPress([this]() {
            ESP_LOGI("PwrButton", "Short Press ...");
        });
        pwr_btn_management_->OnLongPress([this]() {
            ESP_LOGI("PwrButton", "Long Press ...");
        });
        pwr_btn_management_->SetEnabled(true);
    }

    void InitializePowerDetection() {
        ESP_LOGI(TAG, "Initializing power detection");

        power_detect_ = new PowerManagement(power_save_timer_, pmic_);
        power_detect_->SetTempToWarning(39.0f);
        power_detect_->OnEnterChargeMode([this]() {
            ESP_LOGI(TAG, "Enabling charge mode");
            auto display = GetDisplay();
            display->SetChatMessage("system", "充电中!");
            //GetBacklight()->SetBrightness(20);
        });
        power_detect_->OnExitChargeMode([this]() {
            auto display = GetDisplay();
            display->SetChatMessage("system", "已冲满!");
            //display->SetEmotion("neutral");
            GetBacklight()->RestoreBrightness();
        });
        power_detect_->OnNeedCharge([this]() {
            auto display = GetDisplay();
            display->SetChatMessage("system", "需要充电!");
            display->SetEmotion("neutral");
            GetBacklight()->RestoreBrightness();
        });
        power_detect_->OnEnterTooHot([this](float temperature) {
            auto display = GetDisplay();
            char power_info[512];
            snprintf(power_info, sizeof(power_info),
                "温度过高! %.2f°C\n", temperature);
            display->SetChatMessage("system", power_info);
            ShowPowerSourceInfo();
            //display->SetEmotion("neutral");
            GetBacklight()->SetBrightness(20);
        });
        power_detect_->OnExitTooHot([this]() {
            auto display = GetDisplay();
            display->SetChatMessage("system", "温度正常!");
            display->SetEmotion("neutral");
            GetBacklight()->RestoreBrightness();
        });
        power_detect_->SetEnabled(true);
    }

    void InitializeSDCard() {
        ESP_LOGI(TAG, "Initializing SD card");

        /** 暂时禁止IO扩展器控制SD卡
        if (io_expander) {
            esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_1 | IO_EXPANDER_PIN_NUM_2, IO_EXPANDER_OUTPUT);
            esp_io_expander_set_level(io_expander, IO_EXPANDER_PIN_NUM_1, 0);
            esp_io_expander_set_level(io_expander, IO_EXPANDER_PIN_NUM_2, 0);

            vTaskDelay(pdMS_TO_TICKS(200));
            esp_io_expander_set_level(io_expander, IO_EXPANDER_PIN_NUM_1, 1);
            esp_io_expander_set_level(io_expander, IO_EXPANDER_PIN_NUM_2, 1);
        }*/

        // 创建SD卡管理器
        sd_card_manager_ = new SDCardManager();

        // 设置状态回调
        sd_card_manager_->SetStatusCallback([](SDCardManager::Status status, const char* message) {
            ESP_LOGI("SDCard", "Status changed: %s", message ? message : "");
        });

        // 尝试初始化SD卡
        if (sd_card_manager_->Initialize()) {
            ESP_LOGI(TAG, "✅ SD card initialized successfully");

            // 显示SD卡信息
            SDCardUtils::ShowSDCardInfo(*sd_card_manager_);

            // 测试目录创建
            SDCardUtils::TestDirectoryCreation(*sd_card_manager_);

            // 创建示例文件
            SDCardUtils::CreateSampleFiles(*sd_card_manager_);

            // 测试恢复的功能
            SDCardUtils::TestRestoredFunctions(*sd_card_manager_);

            auto display = GetDisplay();
            if (display) {
                char size_str[32];
                SDCardUtils::FormatSize(sd_card_manager_->GetTotalSize(), size_str, sizeof(size_str));

                char msg[128];
                snprintf(msg, sizeof(msg), "SD卡已挂载\n容量: %s", size_str);
                display->SetChatMessage("system", msg);
            }
        } else {
            ESP_LOGE(TAG, "❌ Failed to initialize SD card");

            // 尝试重新挂载
            ESP_LOGI(TAG, "Attempting to remount SD card...");
            if (sd_card_manager_->Remount()) {
                ESP_LOGI(TAG, "✅ SD card remounted successfully");

                auto display = GetDisplay();
                if (display) {
                    display->SetChatMessage("system", "SD卡重新挂载成功");
                }

                // 继续执行初始化
                SDCardUtils::ShowSDCardInfo(*sd_card_manager_);
                SDCardUtils::TestDirectoryCreation(*sd_card_manager_);
                SDCardUtils::CreateSampleFiles(*sd_card_manager_);
            } else {
                ESP_LOGE(TAG, "❌ Failed to remount SD card");

                // 最后尝试格式化
                ESP_LOGW(TAG, "Attempting to format SD card as last resort...");
                if (sd_card_manager_->TryFormatAndRemount()) {
                    ESP_LOGI(TAG, "✅ SD card formatted and mounted successfully");

                    auto display = GetDisplay();
                    if (display) {
                        display->SetChatMessage("system", "SD卡已格式化并挂载\n数据已清空");
                    }

                    // 继续执行初始化
                    SDCardUtils::ShowSDCardInfo(*sd_card_manager_);
                    SDCardUtils::TestDirectoryCreation(*sd_card_manager_);
                    SDCardUtils::CreateSampleFiles(*sd_card_manager_);
                } else {
                    ESP_LOGE(TAG, "❌ All SD card mount attempts failed");

                    auto display = GetDisplay();
                    if (display) {
                        display->SetChatMessage("system", "SD卡无法使用\n请更换SD卡");
                    }
                }
            }
        }
    }

    // 初始化工具
    void InitializeTools() {
        auto &mcp_server = McpServer::GetInstance();
        mcp_server.AddTool("self.system.reconfigure_wifi",
            "Reboot the device and enter WiFi configuration mode.\n"
            "**CAUTION** You must ask the user to confirm this action.",
            PropertyList(), [this](const PropertyList& properties) {
                ResetWifiConfiguration();
                return true;
            });

        // 添加电源信息工具
        mcp_server.AddTool("self.system.show_power_info",
            "Show current power source information (battery/USB).",
            PropertyList(), [this](const PropertyList& properties) {
                ShowPowerSourceInfo();
                return true;
            });
    }

    // 自定义功能：显示系统信息
    void ShowSystemInfo() {
        auto display = GetDisplay();
        if (!display) return;

        // 获取系统信息
        esp_chip_info_t chip_info;
        esp_chip_info(&chip_info);

        uint32_t flash_size;
        esp_flash_get_size(nullptr, &flash_size);

        size_t free_heap = esp_get_free_heap_size();
        size_t min_free_heap = esp_get_minimum_free_heap_size();

        uint32_t uptime = lv_tick_get() / 1000;

        // 获取电池信息
        int battery_level = 0;
        float voltage = 0;
        bool charging = false;
        bool discharging = false;
        if (pmic_) {
            GetBatteryLevel(battery_level, charging, discharging);
            voltage = pmic_->GetBatteryVoltage();
        }

        // 格式化信息字符串
        char menu_text[824];
        snprintf(menu_text, sizeof(menu_text),
            "芯片: %s Rev %d\n"
            "核心数: %d\n"
            "Flash: %ld MB\n"
            "空闲内存: %d KB\n"
            "最小空闲: %d KB\n"
            "运行时间: %" PRIu32 " 秒\n"
            "电池: %d%% %.2fV %s\n"
            "\n"
            "手势操作:\n"
            "← → 切换页面\n"
            "↑ ↓ 调节音量\n"
            "点击: 切换聊天\n"
            "长按: 系统菜单",
            CONFIG_IDF_TARGET,
            chip_info.revision,
            chip_info.cores,
            flash_size / (1024 * 1024),
            free_heap / 1024,
            min_free_heap / 1024,
            uptime,
            battery_level, voltage,
            discharging ? "放电中" : (charging ? "充电中" : "")
        );

        // 显示信息
        display->SetChatMessage("system", menu_text);
        ESP_LOGI(TAG, "%s", menu_text);
    }

    void ShowPowerSourceInfo() {
        ESP_LOGI(TAG, "=== Power Source Info Debug ===");

        auto display = GetDisplay();
        if (!display) return;

        const char* source_name = power_detect_->GetPowerSourceName();

        ESP_LOGI(TAG, "Detected power source: %s", source_name);

        char power_info[512];
        if (pmic_) {
            // 获取电池信息
            int battery_level = 0;
            bool charging = false;
            bool discharging = false;

            GetBatteryLevel(battery_level, charging, discharging);

            float battery_voltage = pmic_->GetBatteryVoltage();

            snprintf(power_info, sizeof(power_info),
                "电源信息\n"
                "供电方式: %s\n"
                "电池: %.2fV (%d%%)\n"
                "充电状态: %s",
                source_name,
                battery_voltage, battery_level,
                discharging ? "放电中" : (charging ? "充电中" : "")
            );
        } else {
            snprintf(power_info, sizeof(power_info),
                "电源信息\n"
                "供电方式: %s\n"
                "GPIO检测: %s",
                source_name,
                "unknown"
            );
        }

        //display->SetChatMessage("system", power_info);
        ESP_LOGI(TAG, "%s", power_info);
    }

    void inline WakeUp() {
        // 唤醒电源管理
        if (power_save_timer_) {
            power_save_timer_->WakeUp();
        }
        GetBacklight()->RestoreBrightness();
    }

    bool IsInWifiConfigMode() {
        return wifi_config_mode_;
    }

#ifdef CONFIG_BT_ENABLED
    void InitializeBluFi(BluFiManager* blufi_manager_) {
        ESP_LOGI(TAG, "Initializing BluFi for WiFi configuration");

        // 设置BluFi回调
        blufi_manager_->SetStateCallback([this](BluFiManager::State state, bool enabled) {
            ESP_LOGI(TAG, "BluFi state changed to: %s", GetBluFiStateName(state));
            auto display = GetDisplay();
            switch (state) {
                case BluFiManager::State::ADVERTISING:
                    display->SetChatMessage("system", "BluFi广播中\n等待WiFi配置...");
                    ESP_LOGI(TAG, "BluFi广播中\n等待WiFi配置...");
                    break;
                case BluFiManager::State::CONNECTED:
                    display->SetChatMessage("system", "BluFi已连接\n准备配置WiFi");
                    ESP_LOGI(TAG, "BluFi已连接\n准备配置WiFi");
                    break;
                case BluFiManager::State::CONFIGURING:
                    display->SetChatMessage("system", "正在配置WiFi\n请稍候...");
                    ESP_LOGI(TAG, "正在配置WiFi\n请稍候...");
                    break;
                case BluFiManager::State::IDLE:
                    display->SetChatMessage("system", "BluFi空闲\n等待连接...");
                    ESP_LOGI(TAG, "BluFi空闲\n等待连接...");
                    break;
                case BluFiManager::State::CONNECTED_TO_WIFI:
                    display->SetChatMessage("system", "已连接到WiFi\n准备启动设备");
                    ESP_LOGI(TAG, "已连接到WiFi, 准备启动设备");
                    // 再等待10000ms确保所有连接都已关闭
                    vTaskDelay(pdMS_TO_TICKS(10000));
                    // 执行重启
                    esp_restart();
                    break;
                default:
                    break;
            }
        });

        blufi_manager_->SetWifiConfigCallback([this](const BluFiManager::WifiConfig& config) {
            ESP_LOGI(TAG, "WiFi config received - SSID: %s, Password: %s", config.ssid, config.password);
        });

        blufi_manager_->SetStatusCallback([this](const char* message) {
            ESP_LOGI(TAG, "BluFi status: %s", message);
        });

        // 尝试初始化BluFi
        if (blufi_manager_->Enable()) {
            ESP_LOGI(TAG, "BluFi initialized successfully");

            auto display = GetDisplay();
            if (display) {
                display->SetChatMessage("system", "BluFi已启用\n可配置WiFi");
            }

            // 开始广播
            //blufi_manager_->StartAdvertising();

            // 显示BluFi信息
            BluFiGuide::ShowBluFiInfo();
            BluFiGuide::ShowConfigurationSteps();

        } else {
            ESP_LOGE(TAG, "Failed to initialize BluFi");

            auto display = GetDisplay();
            if (display) {
                display->SetChatMessage("system", "BluFi初始化失败");
            }
        }
    }
#endif

protected:
#ifdef CONFIG_BT_ENABLED
    void EnterWifiConfigMode() {
        auto& application = Application::GetInstance();
        application.SetDeviceState(kDeviceStateWifiConfiguring);
        /**
         * BluFi管理
         */
        // 创建BluFi管理器
        BluFiManager* blufi_manager_ = new BluFiManager();

        InitializeBluFi(blufi_manager_);

        // 显示 WiFi 配置 AP 的 SSID 和 Web 服务器 URL
        std::string hint = Lang::Strings::CONNECT_TO_HOTSPOT;
        hint += "\n\n";
        hint +=  "📲 使用方法:\n";
        hint +=  "1. 下载EspBlufi App\n";
        hint +=  "2. 扫描并连接设备\n";
        hint +=  "3. 输入WiFi信息\n";
        hint +=  "4. 等待配置完成";
        
        // 播报配置 WiFi 的提示
        application.Alert(Lang::Strings::WIFI_CONFIG_MODE, hint.c_str(), "", Lang::Sounds::OGG_WIFICONFIG);
        
        // Wait forever until reset after configuration
        while (true) {
            vTaskDelay(pdMS_TO_TICKS(10000));
        }
    }
#endif

public:
    WaveshareEsp32s3TouchAMOLED1inch75() : boot_button_(BOOT_BUTTON_GPIO) {
        if (!IsInWifiConfigMode()) {
            InitializePowerSaveTimer();
        }
        InitializeCodecI2c();
        InitializePmic();
        InitializeSpi();
        InitializeSH8601Display();
        InitializeTouch();
        InitializeButtons();

        if (!IsInWifiConfigMode()) {
            // pwr和codec的ES7210冲突
            //InitializePwrBtn();
            InitializeTouchGestures();
            InitializePowerDetection();
            InitializeSDCard();
            InitializeTools();

            // 测试引脚冲突解决方案
            SDCardUtils::TestPinConflictResolution();
        }
    }

    ~WaveshareEsp32s3TouchAMOLED1inch75() {
        // 清理触摸手势处理器
        if (touch_gesture_handler_) {
            delete touch_gesture_handler_;
            touch_gesture_handler_ = nullptr;
        }

        // 清理SD卡管理器
        if (sd_card_manager_) {
            delete sd_card_manager_;
            sd_card_manager_ = nullptr;
        }

        if (pwr_btn_management_) {
            delete pwr_btn_management_;
            pwr_btn_management_ = nullptr;
        }

        ESP_LOGI(TAG, "Resources cleaned up");
    }

    virtual AudioCodec* GetAudioCodec() override {
        static BoxAudioCodec audio_codec(
            i2c_bus_, 
            AUDIO_INPUT_SAMPLE_RATE, 
            AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_MCLK, 
            AUDIO_I2S_GPIO_BCLK, 
            AUDIO_I2S_GPIO_WS, 
            AUDIO_I2S_GPIO_DOUT, 
            AUDIO_I2S_GPIO_DIN,
            AUDIO_CODEC_PA_PIN, 
            AUDIO_CODEC_ES8311_ADDR, 
            AUDIO_CODEC_ES7210_ADDR, 
            AUDIO_INPUT_REFERENCE);
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        return display_;
    }

    virtual Backlight* GetBacklight() override {
        return backlight_;
    }

    virtual bool GetBatteryLevel(int &level, bool &charging, bool &discharging) override {
        static bool last_discharging = false;
        if (pmic_) {
            charging = pmic_->IsCharging();
            discharging = pmic_->IsDischarging();
            if (discharging != last_discharging) {
                if (power_save_timer_) {
                    power_save_timer_->SetEnabled(discharging);
                }
                last_discharging = discharging;
            }

            level = pmic_->GetBatteryLevel();
        } else {
            // 移除AXP2101相关代码，返回默认值
            level = 100;
            charging = false;
            discharging = true;
        }
        return true;
    }

    virtual void SetPowerSaveMode(bool enabled) override {
        if (!enabled)
        {
            WakeUp();
        }
        WifiBoard::SetPowerSaveMode(enabled);
    }
};

DECLARE_BOARD(WaveshareEsp32s3TouchAMOLED1inch75);
