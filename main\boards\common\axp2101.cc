#include "axp2101.h"
#include "board.h"
#include "display.h"

#include <esp_log.h>

#define TAG "Axp2101"

Axp2101::Axp2101(i2c_master_bus_handle_t i2c_bus, uint8_t addr) : I2cDevice(i2c_bus, addr) {
}

int Axp2101::GetBatteryCurrentDirection() {
    return (ReadReg(0x01) & 0b01100000) >> 5;
}

bool Axp2101::IsCharging() {
    return isCharging();
}

bool Axp2101::IsDischarging() {
    return isDischarge();
}

bool Axp2101::IsChargingDone() {
    return getChargerStatus() == XPOWERS_AXP2101_CHG_DONE_STATE;
}

int Axp2101::GetBatteryLevel() {
    return getBatteryPercent();
}

float Axp2101::GetBatteryVoltage() {
    return getBattVoltage() / 1000.0f;
}

float Axp2101::GetTemperature() {
    //uint8_t value = ReadReg(0xA5);
    //float temp = getTemperature();
    //ESP_LOGI(TAG, "temp is: %.2f (readreg: %.2f)", temp, value/100.00f);
    return getTemperature();
}

void Axp2101::PowerOff() {
    uint8_t value = ReadReg(XPOWERS_AXP2101_COMMON_CONFIG);
    value = value | 0x01;
    WriteReg(XPOWERS_AXP2101_COMMON_CONFIG, value);
}

float Axp2101::GetSystemVoltage() {
    return getSystemVoltage() / 1000.0f;
}

bool Axp2101::IsUsbConnected() {
    return isVbusGood();
}

bool Axp2101::IsUsbPowered() {
    return isVbusIn();
}

bool Axp2101::IsBatteryPresent() {
    return isBatteryConnect();
}

float Axp2101::GetUsbVoltage() {
    return getVbusVoltage() / 1000.0f;
}

float Axp2101::GetUsbCurrent() {
    // 读取VBUS电流寄存器 (0x7C-0x7D)
    uint8_t current_h = ReadReg(0x7C);
    uint8_t current_l = ReadReg(0x7D);

    // 合并高低字节并转换为电流值
    uint16_t current_raw = (current_h << 4) | (current_l & 0x0F);

    // VBUS电流分辨率通常为0.375mA
    float current = current_raw * 0.375f; // 转换为毫安

    ESP_LOGD(TAG, "USB current: %.1fmA (raw: %d)", current, current_raw);
    return current;
}

int Axp2101::GetPowerSource() {
    bool usb_connected = IsUsbConnected();
    bool usb_powered = IsUsbPowered();
    bool battery_present = IsBatteryPresent();

    if (usb_powered && battery_present) {
        return 2;
    } else if (usb_powered || usb_connected) {
        return 1;
    } else if (battery_present) {
        return 0;
    } else {
        return -1;
    }
}
