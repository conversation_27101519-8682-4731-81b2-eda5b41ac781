#pragma once

#include <esp_log.h>
#include <functional>
#include "ble_debug_helper.h"

#ifdef CONFIG_BT_ENABLED

#include <esp_bt.h>
#include <esp_bt_main.h>
#include <esp_gap_ble_api.h>
#include <esp_gatt_common_api.h>
#include <esp_gatts_api.h>

/**
 * @brief 无安全BLE管理器 - 完全跳过安全认证
 */
class BleManager {
public:
    enum class State {
        DISABLED,
        IDLE,
        ADVERTISING,
        SCANNING,
        CONNECTED
    };

    struct Device {
        char name[32];
        esp_bd_addr_t address;
        int rssi;
        bool is_connected;
    };

    using StateCallback = std::function<void(State state)>;
    using DeviceCallback = std::function<void(const Device& device)>;
    using DataCallback = std::function<void(const uint8_t* data, size_t len)>;

    BleManager();
    ~BleManager();

    bool Initialize();
    bool Enable();
    bool Disable();
    
    bool StartAdvertising();
    bool StopAdvertising();
    bool StartScanning(int duration_sec = 10);
    bool StopScanning();
    
    void SetDeviceName(const char* name);
    State GetState() const { return current_state_; }
    bool IsEnabled() const { return is_initialized_; }
    Device GetConnectedDevice() const { return connected_device_; }
    
    void SetStateCallback(StateCallback callback) { state_callback_ = callback; }
    void SetDeviceCallback(DeviceCallback callback) { device_callback_ = callback; }
    void SetDataCallback(DataCallback callback) { data_callback_ = callback; }

    // GATT服务相关方法
    bool SendNotification(const uint8_t* data, size_t len);
    bool SendNotification(const char* text);

private:
    // 初始化函数 - 跳过所有安全配置
    bool InitializeWithoutSecurity();
    bool SetupBasicAdvertisingData();
    bool CreateGattService();
    
    // 事件处理 - 忽略所有安全事件
    static void GapEventHandler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param);
    static void GattsEventHandler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param);
    
    void HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param);
    void HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param);
    
    void UpdateState(State new_state);

private:
    State current_state_;
    Device connected_device_;
    bool is_initialized_;
    uint16_t gatts_if_;
    uint16_t conn_id_;

    // GATT服务相关
    uint16_t service_handle_;
    uint16_t char_handle_;
    uint16_t descr_handle_;
    
    StateCallback state_callback_;
    DeviceCallback device_callback_;
    DataCallback data_callback_;
    
    static BleManager* instance_;
    static const char* TAG;
};

#else

/**
 * @brief 简化版本（当BLE未启用时）
 */
class BleManager {
public:
    enum class State { DISABLED };
    
    BleManager() {}
    ~BleManager() {}
    
    bool Initialize() {
        ESP_LOGW("BleManager", "BLE not enabled in configuration");
        return false;
    }
    
    bool IsEnabled() const { return false; }
    State GetState() const { return State::DISABLED; }
    
    void SetDeviceName(const char* name) {}
    bool StartAdvertising() { return false; }
    bool StartScanning(int duration_sec = 10) { return false; }
};

#endif

/**
 * @brief 获取状态名称
 */
const char* GetBleStateName(BleManager::State state);

/**
 * @brief 无安全BLE配置指南
 */
class BleGuide {
public:
    static void ShowNoSecurityInfo() {
        ESP_LOGI("BleGuide", "=== 无安全BLE连接模式 ===");
        ESP_LOGI("BleGuide", "");
        ESP_LOGI("BleGuide", "🔓 特点:");
        ESP_LOGI("BleGuide", "- 完全跳过安全管理器初始化");
        ESP_LOGI("BleGuide", "- 忽略所有安全相关事件");
        ESP_LOGI("BleGuide", "- 使用最基础的BLE连接");
        ESP_LOGI("BleGuide", "- 无任何认证或加密");
        ESP_LOGI("BleGuide", "");
        ESP_LOGI("BleGuide", "✅ 优势:");
        ESP_LOGI("BleGuide", "- 连接成功率最高");
        ESP_LOGI("BleGuide", "- 无配对问题");
        ESP_LOGI("BleGuide", "- 兼容性最好");
        ESP_LOGI("BleGuide", "- 连接速度最快");
        ESP_LOGI("BleGuide", "");
        ESP_LOGI("BleGuide", "⚠️  注意:");
        ESP_LOGI("BleGuide", "- 仅适用于开发测试");
        ESP_LOGI("BleGuide", "- 数据完全未加密");
        ESP_LOGI("BleGuide", "- 任何设备都可连接");
        ESP_LOGI("BleGuide", "");
    }
    
    static void ShowConnectionSuccess() {
        ESP_LOGI("BleGuide", "🎉 无安全模式连接成功!");
        ESP_LOGI("BleGuide", "现在可以:");
        ESP_LOGI("BleGuide", "- 发现GATT服务");
        ESP_LOGI("BleGuide", "- 读写特征值");
        ESP_LOGI("BleGuide", "- 接收通知");
        ESP_LOGI("BleGuide", "- 发送数据");
    }
};

/**
 * @brief BLE配置检查和修复指南
 */
class BleConfigHelper {
public:
    static void ShowCompatibilityInfo() {
        ESP_LOGI("BleConfigHelper", "=== BLE兼容性配置指南 ===");
        ESP_LOGI("BleConfigHelper", "");
        ESP_LOGI("BleConfigHelper", "🔧 推荐配置 (sdkconfig):");
        ESP_LOGI("BleConfigHelper", "CONFIG_BT_ENABLED=y");
        ESP_LOGI("BleConfigHelper", "CONFIG_BT_BLUEDROID_ENABLED=y");
        ESP_LOGI("BleConfigHelper", "CONFIG_BT_BLE_ENABLED=y");
        ESP_LOGI("BleConfigHelper", "CONFIG_BT_BLE_42_FEATURES_SUPPORTED=y  # 重要!");
        ESP_LOGI("BleConfigHelper", "CONFIG_BT_BLE_50_FEATURES_SUPPORTED=y  # 可选");
        ESP_LOGI("BleConfigHelper", "");
        ESP_LOGI("BleConfigHelper", "⚠️  常见问题:");
        ESP_LOGI("BleConfigHelper", "1. 某些API需要BLE 4.2兼容性");
        ESP_LOGI("BleConfigHelper", "2. 纯BLE 5.0模式可能缺少某些函数");
        ESP_LOGI("BleConfigHelper", "3. 建议同时启用4.2和5.0支持");
        ESP_LOGI("BleConfigHelper", "");
        ESP_LOGI("BleConfigHelper", "🛠️  修复步骤:");
        ESP_LOGI("BleConfigHelper", "1. idf.py menuconfig");
        ESP_LOGI("BleConfigHelper", "2. Component config -> Bluetooth -> BLE");
        ESP_LOGI("BleConfigHelper", "3. 启用 'BLE 4.2 features'");
        ESP_LOGI("BleConfigHelper", "4. 保持 'BLE 5.0 features' 启用");
        ESP_LOGI("BleConfigHelper", "5. idf.py build");
        ESP_LOGI("BleConfigHelper", "");
    }
    
    static void CheckCurrentConfig() {
        ESP_LOGI("BleConfigHelper", "=== 当前BLE配置检查 ===");
        
#ifdef CONFIG_BT_ENABLED
        ESP_LOGI("BleConfigHelper", "✅ CONFIG_BT_ENABLED");
#else
        ESP_LOGW("BleConfigHelper", "❌ CONFIG_BT_ENABLED");
#endif

#ifdef CONFIG_BT_BLUEDROID_ENABLED
        ESP_LOGI("BleConfigHelper", "✅ CONFIG_BT_BLUEDROID_ENABLED");
#else
        ESP_LOGW("BleConfigHelper", "❌ CONFIG_BT_BLUEDROID_ENABLED");
#endif

#ifdef CONFIG_BT_BLE_ENABLED
        ESP_LOGI("BleConfigHelper", "✅ CONFIG_BT_BLE_ENABLED");
#else
        ESP_LOGW("BleConfigHelper", "❌ CONFIG_BT_BLE_ENABLED");
#endif

#ifdef CONFIG_BT_BLE_42_FEATURES_SUPPORTED
        ESP_LOGI("BleConfigHelper", "✅ CONFIG_BT_BLE_42_FEATURES_SUPPORTED");
#else
        ESP_LOGW("BleConfigHelper", "❌ CONFIG_BT_BLE_42_FEATURES_SUPPORTED - 这可能导致API问题!");
#endif

#ifdef CONFIG_BT_BLE_50_FEATURES_SUPPORTED
        ESP_LOGI("BleConfigHelper", "✅ CONFIG_BT_BLE_50_FEATURES_SUPPORTED");
#else
        ESP_LOGI("BleConfigHelper", "ℹ️  CONFIG_BT_BLE_50_FEATURES_SUPPORTED (可选)");
#endif

        ESP_LOGI("BleConfigHelper", "");
    }
};
