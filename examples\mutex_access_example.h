#ifndef MUTEX_ACCESS_EXAMPLE_H
#define MUTEX_ACCESS_EXAMPLE_H

#include <mutex>
#include <memory>
#include <esp_timer.h>
#include <esp_log.h>

/**
 * 线程安全的共享资源管理器
 * 适用于多个timer或任务同时访问共享指针的场景
 */
template<typename T>
class ThreadSafeResource {
public:
    ThreadSafeResource() = default;
    
    explicit ThreadSafeResource(std::shared_ptr<T> resource) 
        : resource_(resource) {}
    
    // 安全地设置资源
    void SetResource(std::shared_ptr<T> resource) {
        std::lock_guard<std::mutex> lock(mutex_);
        resource_ = resource;
    }
    
    // 安全地获取资源（返回副本，避免悬空指针）
    std::shared_ptr<T> GetResource() {
        std::lock_guard<std::mutex> lock(mutex_);
        return resource_;
    }
    
    // 安全地执行操作（推荐方式）
    template<typename Func>
    auto SafeExecute(Func&& func) -> decltype(func(resource_)) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (resource_) {
            return func(resource_);
        }
        return decltype(func(resource_)){}; // 返回默认值
    }
    
    // 检查资源是否存在
    bool IsValid() {
        std::lock_guard<std::mutex> lock(mutex_);
        return resource_ != nullptr;
    }
    
    // 重置资源
    void Reset() {
        std::lock_guard<std::mutex> lock(mutex_);
        resource_.reset();
    }

private:
    std::shared_ptr<T> resource_;
    mutable std::mutex mutex_;
};

/**
 * ESP32 Timer管理器示例
 * 展示如何在多个timer中安全访问共享资源
 */
class TimerManager {
public:
    TimerManager();
    ~TimerManager();
    
    // 启动定时器
    bool StartTimers();
    
    // 停止定时器
    void StopTimers();
    
    // 设置共享数据
    void SetSharedData(std::shared_ptr<std::string> data);
    
private:
    // 共享资源
    ThreadSafeResource<std::string> shared_data_;
    
    // ESP32定时器句柄
    esp_timer_handle_t timer1_handle_;
    esp_timer_handle_t timer2_handle_;
    
    // 定时器回调函数
    static void Timer1Callback(void* arg);
    static void Timer2Callback(void* arg);
    
    // 实际的处理函数
    void HandleTimer1();
    void HandleTimer2();
    
    static const char* TAG;
};

/**
 * FreeRTOS任务安全访问示例
 */
class TaskManager {
public:
    TaskManager();
    ~TaskManager();
    
    bool StartTasks();
    void StopTasks();
    void SetSharedResource(std::shared_ptr<int> resource);
    
private:
    ThreadSafeResource<int> shared_resource_;
    TaskHandle_t task1_handle_;
    TaskHandle_t task2_handle_;
    bool should_stop_;
    
    static void Task1Function(void* arg);
    static void Task2Function(void* arg);
    
    void HandleTask1();
    void HandleTask2();
    
    static const char* TAG;
};

#endif // MUTEX_ACCESS_EXAMPLE_H
