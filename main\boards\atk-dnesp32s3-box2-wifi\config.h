#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

enum PowerSupply {
    kDeviceTypecSupply,
    kDeviceBatterySupply,
};

#define AUDIO_INPUT_SAMPLE_RATE 16000
#define AUDIO_OUTPUT_SAMPLE_RATE 16000

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_38
#define AUDIO_I2S_GPIO_WS GPIO_NUM_42
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_40
#define AUDIO_I2S_GPIO_DIN GPIO_NUM_39
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_41

#define AUDIO_CODEC_I2C_SDA_PIN GPIO_NUM_48
#define AUDIO_CODEC_I2C_SCL_PIN GPIO_NUM_47
#define AUDIO_CODEC_ES8389_ADDR ES8389_CODEC_DEFAULT_ADDR

#define R_BUTTON_GPIO GPIO_NUM_0

#define XL9555_INT_GPIO GPIO_NUM_2
#define XIO_IO_SBU2 (IO_EXPANDER_PIN_NUM_3)
#define XIO_IO_SBU1 (IO_EXPANDER_PIN_NUM_4)
#define XIO_KEY_L (IO_EXPANDER_PIN_NUM_5)
#define XIO_KEY_Q (IO_EXPANDER_PIN_NUM_6)
#define XIO_KEY_M (IO_EXPANDER_PIN_NUM_7)
#define XIO_USB_SEL (IO_EXPANDER_PIN_NUM_8)
#define XIO_SPK_EN (IO_EXPANDER_PIN_NUM_9)
#define XIO_SYS_POW (IO_EXPANDER_PIN_NUM_10)
#define XIO_VBUS_EN (IO_EXPANDER_PIN_NUM_11)
#define XIO_EN_4G (IO_EXPANDER_PIN_NUM_12)
#define XIO_EN_3V3A (IO_EXPANDER_PIN_NUM_13)
#define XIO_CHG_CTRL (IO_EXPANDER_PIN_NUM_14)
#define XIO_CHRG (IO_EXPANDER_PIN_NUM_15)

#define DRV_IO_EXP_OUTPUT_MASK 0x3F18  
#define DRV_IO_EXP_INPUT_MASK 0xC0E7

#define LCD_PIN_CS GPIO_NUM_14
#define LCD_PIN_DC GPIO_NUM_12
#define LCD_PIN_RD GPIO_NUM_10
#define LCD_PIN_WR GPIO_NUM_11
#define LCD_PIN_RST GPIO_NUM_NC
#define LCD_PIN_D0 GPIO_NUM_13
#define LCD_PIN_D1 GPIO_NUM_9
#define LCD_PIN_D2 GPIO_NUM_8
#define LCD_PIN_D3 GPIO_NUM_7
#define LCD_PIN_D4 GPIO_NUM_6
#define LCD_PIN_D5 GPIO_NUM_5
#define LCD_PIN_D6 GPIO_NUM_4
#define LCD_PIN_D7 GPIO_NUM_3

#define DISPLAY_WIDTH 240
#define DISPLAY_HEIGHT 320
#define DISPLAY_MIRROR_X false
#define DISPLAY_MIRROR_Y false
#define DISPLAY_SWAP_XY false

#define DISPLAY_OFFSET_X 0
#define DISPLAY_OFFSET_Y 0

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_21
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false

#endif // _BOARD_CONFIG_H_

