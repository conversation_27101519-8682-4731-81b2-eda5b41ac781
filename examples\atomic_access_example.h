#ifndef ATOMIC_ACCESS_EXAMPLE_H
#define ATOMIC_ACCESS_EXAMPLE_H

#include <atomic>
#include <memory>
#include <esp_timer.h>
#include <esp_log.h>

/**
 * 使用原子操作的线程安全计数器
 * 适用于简单数据类型的高性能访问
 */
class AtomicCounter {
public:
    AtomicCounter(int initial_value = 0) : value_(initial_value) {}
    
    // 原子递增
    int Increment() {
        return value_.fetch_add(1) + 1;
    }
    
    // 原子递减
    int Decrement() {
        return value_.fetch_sub(1) - 1;
    }
    
    // 原子加法
    int Add(int delta) {
        return value_.fetch_add(delta) + delta;
    }
    
    // 原子设置
    void Set(int new_value) {
        value_.store(new_value);
    }
    
    // 原子获取
    int Get() const {
        return value_.load();
    }
    
    // 比较并交换
    bool CompareAndSwap(int expected, int desired) {
        return value_.compare_exchange_strong(expected, desired);
    }
    
    // 原子交换
    int Exchange(int new_value) {
        return value_.exchange(new_value);
    }

private:
    std::atomic<int> value_;
};

/**
 * 使用原子指针的线程安全资源管理
 * 注意：这种方式有ABA问题的风险，需要谨慎使用
 */
template<typename T>
class AtomicPointer {
public:
    AtomicPointer() : ptr_(nullptr) {}
    
    explicit AtomicPointer(T* ptr) : ptr_(ptr) {}
    
    // 原子设置指针
    void Set(T* new_ptr) {
        ptr_.store(new_ptr);
    }
    
    // 原子获取指针
    T* Get() const {
        return ptr_.load();
    }
    
    // 比较并交换指针
    bool CompareAndSwap(T* expected, T* desired) {
        return ptr_.compare_exchange_strong(expected, desired);
    }
    
    // 原子交换指针
    T* Exchange(T* new_ptr) {
        return ptr_.exchange(new_ptr);
    }
    
    // 安全访问（需要外部保证指针生命周期）
    template<typename Func>
    bool SafeAccess(Func&& func) {
        T* current = Get();
        if (current) {
            func(current);
            return true;
        }
        return false;
    }

private:
    std::atomic<T*> ptr_;
};

/**
 * Lock-Free队列（简化版本）
 * 适用于单生产者单消费者场景
 */
template<typename T>
class LockFreeQueue {
public:
    explicit LockFreeQueue(size_t capacity) 
        : capacity_(capacity)
        , buffer_(new T[capacity])
        , head_(0)
        , tail_(0) {
    }
    
    ~LockFreeQueue() {
        delete[] buffer_;
    }
    
    // 入队（生产者调用）
    bool Enqueue(const T& item) {
        size_t current_tail = tail_.load();
        size_t next_tail = (current_tail + 1) % capacity_;
        
        if (next_tail == head_.load()) {
            return false; // 队列满
        }
        
        buffer_[current_tail] = item;
        tail_.store(next_tail);
        return true;
    }
    
    // 出队（消费者调用）
    bool Dequeue(T& item) {
        size_t current_head = head_.load();
        
        if (current_head == tail_.load()) {
            return false; // 队列空
        }
        
        item = buffer_[current_head];
        head_.store((current_head + 1) % capacity_);
        return true;
    }
    
    // 检查是否为空
    bool IsEmpty() const {
        return head_.load() == tail_.load();
    }
    
    // 检查是否已满
    bool IsFull() const {
        size_t current_tail = tail_.load();
        size_t next_tail = (current_tail + 1) % capacity_;
        return next_tail == head_.load();
    }
    
    // 获取大小（近似值）
    size_t Size() const {
        size_t h = head_.load();
        size_t t = tail_.load();
        return (t >= h) ? (t - h) : (capacity_ - h + t);
    }

private:
    const size_t capacity_;
    T* buffer_;
    std::atomic<size_t> head_;
    std::atomic<size_t> tail_;
};

/**
 * 原子操作示例管理器
 */
class AtomicExampleManager {
public:
    AtomicExampleManager();
    ~AtomicExampleManager();
    
    bool StartExample();
    void StopExample();
    
    // 获取统计信息
    int GetCounter1() const { return counter1_.Get(); }
    int GetCounter2() const { return counter2_.Get(); }
    size_t GetQueueSize() const { return message_queue_.Size(); }

private:
    AtomicCounter counter1_;
    AtomicCounter counter2_;
    LockFreeQueue<std::string> message_queue_;
    
    esp_timer_handle_t timer1_handle_;
    esp_timer_handle_t timer2_handle_;
    esp_timer_handle_t consumer_timer_handle_;
    
    static void Timer1Callback(void* arg);
    static void Timer2Callback(void* arg);
    static void ConsumerCallback(void* arg);
    
    void HandleTimer1();
    void HandleTimer2();
    void HandleConsumer();
    
    static const char* TAG;
};

/**
 * 性能比较测试
 */
class PerformanceComparison {
public:
    static void RunComparison();
    
private:
    static void TestMutexPerformance();
    static void TestAtomicPerformance();
    static void TestFreeRTOSMutexPerformance();
    
    static const char* TAG;
    static const int TEST_ITERATIONS;
};

#endif // ATOMIC_ACCESS_EXAMPLE_H
