#include "gif_display.h"
#include <esp_log.h>

static const char* TAG = "GifDisplay";

GifDisplay::GifDisplay(Display* display)
    : img_obj_(nullptr)
    , timer_(nullptr)
    , frames_(nullptr)
    , frame_count_(0)
    , current_frame_(0)
    , frame_duration_(100)
    , is_playing_(false)
    , is_loop_(true)
    , is_paused_(false)
{
    img_obj_ = display;
    // 设置默认属性
    //lv_obj_set_size(img_obj_, 100, 100);
    //lv_obj_center(img_obj_);
    
    ESP_LOGI(TAG, "GifDisplay created");
}

GifDisplay::~GifDisplay()
{
    StopAnimation();
    
    ESP_LOGI(TAG, "GifDisplay destroyed");
}

bool GifDisplay::LoadGif(const lv_image_dsc_t** frames, int frame_count, int frame_duration)
{
    if (!frames || frame_count <= 0) {
        ESP_LOGE(TAG, "Invalid parameters");
        return false;
    }
    
    // 停止当前动画
    StopAnimation();
    
    frames_ = frames;
    frame_count_ = frame_count;
    frame_duration_ = frame_duration;
    current_frame_ = 0;
    
    // 显示第一帧
    if (img_obj_ && frames_[0]) {
        img_obj_->SetPreviewImage(frames_[0]);
    }
    
    ESP_LOGI(TAG, "GIF loaded: %d frames, %d ms per frame", frame_count_, frame_duration_);
    return true;
}

void GifDisplay::StartAnimation(bool loop)
{
    if (!frames_ || frame_count_ <= 0) {
        ESP_LOGE(TAG, "No GIF loaded");
        return;
    }
    
    if (is_playing_) {
        ESP_LOGW(TAG, "Animation already playing");
        return;
    }
    
    is_loop_ = loop;
    is_playing_ = true;
    is_paused_ = false;
    current_frame_ = 0;
    
    // 创建定时器
    esp_timer_create_args_t timer_args = {
        .callback = [](void* arg) {
            auto self = static_cast<GifDisplay*>(arg);
            self->TimerCallback();
        },
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "gif_show_timer",
        .skip_unhandled_events = true,
    };
    ESP_ERROR_CHECK(esp_timer_create(&timer_args, &timer_));
    ESP_ERROR_CHECK(esp_timer_start_periodic(timer_, frame_duration_));
    if (!timer_) {
        ESP_LOGE(TAG, "Failed to create timer");
        is_playing_ = false;
        return;
    }
    
    ESP_LOGI(TAG, "Animation started (loop: %s)", loop ? "yes" : "no");
}

void GifDisplay::StopAnimation()
{
    if (!is_playing_) {
        return;
    }
    
    is_playing_ = false;
    is_paused_ = false;
    
    if (timer_ != nullptr) {
        esp_timer_stop(timer_);
        esp_timer_delete(timer_);
    }

    // 重置到第一帧
    current_frame_ = 0;
    if (img_obj_ && frames_ && frames_[0]) {
        img_obj_->SetPreviewImage(frames_[0]);
    }
    
    ESP_LOGI(TAG, "Animation stopped");
}

void GifDisplay::PauseAnimation(bool pause)
{
    if (!is_playing_) {
        return;
    }
    
    is_paused_ = pause;
    
    if (timer_) {
        if (pause) {
            esp_timer_stop(timer_);
        } else {
            ESP_ERROR_CHECK(esp_timer_start_periodic(timer_, frame_duration_));
        }
    }
    
    ESP_LOGI(TAG, "Animation %s", pause ? "paused" : "resumed");
}

void GifDisplay::SetPosition(int x, int y, int width, int height)
{
    if (!img_obj_) {
        return;
    }
    
    //lv_obj_set_pos(img_obj_, x, y);
    //lv_obj_set_size(img_obj_, width, height);
}

void GifDisplay::TimerCallback()
{
    UpdateFrame();
}

void GifDisplay::UpdateFrame()
{
    if (!is_playing_ || is_paused_ || !frames_ || !img_obj_) {
        return;
    }
    
    // 更新到下一帧
    current_frame_++;
    
    // 检查是否到达末尾
    if (current_frame_ >= frame_count_) {
        if (is_loop_) {
            current_frame_ = 0;  // 循环播放
        } else {
            StopAnimation();     // 停止播放
            return;
        }
    }
    
    // 显示当前帧
    if (frames_[current_frame_]) {
        img_obj_->SetPreviewImage(frames_[current_frame_]);
    }
}

// 便捷函数实现
GifDisplay* CreateGifAnimation(Display* display, 
                              const lv_image_dsc_t** frames, 
                              int frame_count,
                              int x, int y, 
                              int width, int height,
                              bool loop)
{
    GifDisplay* gif = new GifDisplay(display);
    if (!gif) {
        return nullptr;
    }
    
    if (!gif->LoadGif(frames, frame_count)) {
        delete gif;
        return nullptr;
    }
    
    gif->SetPosition(x, y, width, height);
    gif->StartAnimation(loop);
    
    return gif;
}
