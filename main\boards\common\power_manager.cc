#include "power_manager.h"

static const char* TAG = "PowerManagement";

PowerManagement::PowerManagement(PowerSaveTimer* timer
    , Pmic* pmic
    , float voltage_to_charge
    , int battery_level_to_charge
    , float voltage_to_shutdown
    , int battery_level_to_shutdown
    , float temp_to_warning
)
        : power_save_timer_(timer)
        , pmic_(pmic)
        , monitor_period_(50000000) // 50s
        , on_enter_charge_mode_(nullptr)
        , on_exit_charge_mode_(nullptr)
        , on_need_charge_(nullptr)
{
    SetVoltageToCharge(voltage_to_charge);
    SetBatteryLevelToCharge(battery_level_to_charge);
    SetVoltageToShutdown(voltage_to_shutdown);
    SetBatteryLevelToShutdown(battery_level_to_shutdown);
    SetTempToWarning(temp_to_warning);

    if (pmic_) {
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                auto self = static_cast<PowerManagement*>(arg);
                self->OnPowerMonitorTimer();
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "power_monitor_timer",
            .skip_unhandled_events = true,
        };
        ESP_ERROR_CHECK(esp_timer_create(&timer_args, &monitor_timer_));
    }
}

PowerManagement::~PowerManagement(){
    if (monitor_timer_) {
        ESP_ERROR_CHECK(esp_timer_delete(monitor_timer_));
        monitor_timer_ = nullptr;
    }
}

void PowerManagement::SetEnabled(bool enabled) {
    if (enabled && !enabled_) {
        enabled_ = enabled;
        if (pmic_) {
            ESP_ERROR_CHECK(esp_timer_start_periodic(monitor_timer_, monitor_period_));
            ESP_LOGI(TAG, "Power detect timer enabled");
        }
    } else if (!enabled && enabled_) {
        if (pmic_) {
            ESP_ERROR_CHECK(esp_timer_stop(monitor_timer_));
        }
        enabled_ = enabled;
        ESP_LOGI(TAG, "Power detect timer disabled");
    }
}

void PowerManagement::OnEnterChargeMode(std::function<void()> callback) {
    on_enter_charge_mode_ = callback;
}

void PowerManagement::OnExitChargeMode(std::function<void()> callback) {
    on_exit_charge_mode_ = callback;
}

void PowerManagement::OnNeedCharge(std::function<void()> callback) {
    on_need_charge_ = callback;
}

void PowerManagement::OnEnterTooHot(std::function<void(float temperature)> callback) {
    on_enter_too_hot_ = callback;
}

void PowerManagement::OnExitTooHot(std::function<void()> callback) {
    on_exit_too_hot_ = callback;
}

/**
 * @brief 电源监控定时器回调函数
 */
void PowerManagement::OnPowerMonitorTimer() {
    if (!pmic_) {
        ESP_LOGW(TAG, "PMIC not available");
        return;
    }
    
    PowerSourceType sourceType = GetCurrentPowerSource();
    const char* source_name = GetPowerSourceName(sourceType);
    ESP_LOGI(TAG, "Detected power source: %s, value: %d", source_name, (int)sourceType);

    float temperature = pmic_->GetTemperature();
    bool is_too_hot_warning = (temperature && (temperature > temp_to_warning_));

    // 获取电池电压
    float voltage = pmic_->GetBatteryVoltage();
    ESP_LOGI(TAG, "Battery voltage: %.3f V", voltage);
    
    // 获取其他电池信息进行对比
    int battery_level = pmic_->GetBatteryLevel();
    bool charging = pmic_->IsCharging();
    bool discharging = pmic_->IsDischarging();
    bool charging_done = pmic_->IsChargingDone();
    
    ESP_LOGI(TAG, "Battery level: %d%%", battery_level);
    ESP_LOGI(TAG, "Charging: %s", charging ? "Yes" : "No");
    ESP_LOGI(TAG, "Discharging: %s", discharging ? "Yes" : "No");
    ESP_LOGI(TAG, "Charging done: %s", charging_done ? "Yes" : "No");
    ESP_LOGI(TAG, "Health: %s, %.2f%%", pmic_->IsBatteryHealthy() ? "Yes" : "No", pmic_->GetBatteryHealth()*100);
    ESP_LOGI(TAG, "Temperature: %.2f°C", temperature);
    ESP_LOGI(TAG, "Temperature too hot: %s", is_too_hot_warning ? "Yes" : "No");
    ESP_LOGI(TAG, "Temperature already warning: %s", is_too_hot_warning_ ? "Yes" : "No");

    if (is_too_hot_warning && !is_too_hot_warning_) {
        // 温度开始过高
        is_too_hot_warning_ = is_too_hot_warning;
        ESP_LOGI(TAG, "温度开始过高");
        if (on_enter_too_hot_) {
            cnt_enter_too_hot_ ++;
            on_enter_too_hot_(temperature);
        }
    } else if (!is_too_hot_warning && is_too_hot_warning_) {
        // 温度恢复正常
        is_too_hot_warning_ = is_too_hot_warning;
        ESP_LOGI(TAG, "温度恢复正常");
        if (on_exit_too_hot_) {
            cnt_exit_too_hot_ ++;
            on_exit_too_hot_();
        }
    }

    if (charging && !is_charging_) {
        // 开始充电
        is_charging_ = charging;
        ESP_LOGI(TAG, "开始充电");
        if (power_save_timer_) {
            power_save_timer_->SetEnabled(false);
        }
        if (on_enter_charge_mode_) {
            cnt_enter_charge_ ++;
            on_enter_charge_mode_();
        }
    } else if (!charging && is_charging_) {
        // 停止充电
        is_charging_ = charging;
        ESP_LOGI(TAG, "停止充电");
        if (power_save_timer_) {
            power_save_timer_->SetEnabled(true);
        }
        if (on_exit_charge_mode_) {
            cnt_leave_charge_ ++;
            on_exit_charge_mode_();
        }
    }

    // 根据电池状态调整电源管理策略
    if (charging) {
        ESP_LOGI(TAG, "Charging detected - normal power management");
        
        // 充电时可以使用正常的电源管理策略
        if (power_save_timer_ && power_save_timer_->IsInSleepMode()) {
            power_save_timer_->WakeUp();
        }

    } else if (sourceType == PowerSourceType::USB_AND_BATTERY || sourceType == PowerSourceType::USB_ONLY) {
        ESP_LOGI(TAG, "USB power detected - normal power management");

    } else if (voltage < voltage_to_shutdown_ || battery_level < battery_level_to_shutdown_) {
        ESP_LOGW(TAG, "Low battery detected - 需要关机");

        if (power_save_timer_) {
            cnt_shutdown_ ++;
            power_save_timer_->EnterShutdownMode();
        }

    } else if (voltage < voltage_to_charge_ || battery_level < battery_level_to_charge_) {
        ESP_LOGW(TAG, "Low battery detected - 需要充电");

        if (on_need_charge_) {
            cnt_need_charge_ ++;
            on_need_charge_();
        }

    } else if (voltage < pmic_->BATTERY_NOMINAL_VOLTAGE || battery_level < pmic_->BATTERY_LOW_LEVEL) {
        ESP_LOGW(TAG, "Low battery detected - entering aggressive power save mode");
        
        // 立即进入睡眠模式
        if (power_save_timer_ && !power_save_timer_->IsInSleepMode()) {
            power_save_timer_->EnterSleepMode();
        }
        
        // 可以在这里添加更多节能措施
        // - 降低CPU频率
        // - 关闭非必要外设
        // - 降低显示亮度
        
    } else if (voltage > pmic_->BATTERY_NOMINAL_VOLTAGE && battery_level > pmic_->BATTERY_NOMINAL_LEVEL) {
        ESP_LOGI(TAG, "Good battery level - standard power management");
        
        // 电池状态良好，使用标准电源管理
        
    } else {
        ESP_LOGI(TAG, "Medium battery level - conservative power management");
        
        // 中等电池状态，使用保守的电源管理策略
    }

    ESP_LOGI(TAG, "cnt_enter_charge: %d", cnt_enter_charge_);
    ESP_LOGI(TAG, "cnt_leave_charge: %d", cnt_leave_charge_);
    ESP_LOGI(TAG, "cnt_shutdown: %d", cnt_shutdown_);
    ESP_LOGI(TAG, "cnt_enter_too_hot: %d", cnt_enter_too_hot_);
    ESP_LOGI(TAG, "cnt_exit_too_hot: %d", cnt_exit_too_hot_);
    ESP_LOGI(TAG, "cnt_need_charge: %d", cnt_need_charge_);
}

PowerSourceType PowerManagement::GetCurrentPowerSource() {
    // 使用PMIC进行精确检测
    if (!pmic_) {
        return PowerSourceType::UNKNOWN;
    }
    int power_source = pmic_->GetPowerSource();

    switch (power_source) {
        case 0: return PowerSourceType::BATTERY_ONLY;
        case 1: return PowerSourceType::USB_ONLY;
        case 2: return PowerSourceType::USB_AND_BATTERY;
        default: return PowerSourceType::UNKNOWN;
    }
}

const char* PowerManagement::GetPowerSourceName(PowerSourceType type) {
    switch (type) {
        case PowerSourceType::BATTERY_ONLY: return "仅电池";
        case PowerSourceType::USB_ONLY: return "仅USB";
        case PowerSourceType::USB_AND_BATTERY: return "USB+电池";
        default: return "未知";
    }
}

const char* PowerManagement::GetPowerSourceName() {
    return GetPowerSourceName(GetCurrentPowerSource());
}


void PowerManagement::SetVoltageToCharge(float voltage_to_charge) {
    if (pmic_ && voltage_to_charge < pmic_->BATTERY_MIN_VOLTAGE) {
        voltage_to_charge = pmic_->BATTERY_MIN_VOLTAGE;
    }
    if (pmic_ && voltage_to_charge > pmic_->BATTERY_MAX_VOLTAGE) {
        voltage_to_charge = pmic_->BATTERY_MAX_VOLTAGE;
    }
    voltage_to_charge_ = voltage_to_charge;
}
void PowerManagement::SetVoltageToShutdown(float voltage_to_shutdown) {
    if (pmic_ && voltage_to_shutdown < pmic_->BATTERY_MIN_VOLTAGE) {
        voltage_to_shutdown = pmic_->BATTERY_MIN_VOLTAGE;
    }
    if (pmic_ && voltage_to_shutdown > pmic_->BATTERY_MAX_VOLTAGE) {
        voltage_to_shutdown = pmic_->BATTERY_MAX_VOLTAGE;
    }
    voltage_to_shutdown_ = voltage_to_shutdown;
}

void PowerManagement::SetBatteryLevelToCharge(int battery_level_to_charge) {
    if (pmic_ && battery_level_to_charge > pmic_->BATTERY_NOMINAL_LEVEL) {
        battery_level_to_charge = pmic_->BATTERY_NOMINAL_LEVEL;
    }
    battery_level_to_charge_ = battery_level_to_charge;
}
void PowerManagement::SetBatteryLevelToShutdown(int battery_level_to_shutdown) {
    if (pmic_ && battery_level_to_shutdown > pmic_->BATTERY_LOW_LEVEL) {
        battery_level_to_shutdown = pmic_->BATTERY_LOW_LEVEL;
    }
    battery_level_to_shutdown_ = battery_level_to_shutdown;
}

void PowerManagement::SetTempToWarning(float temp_to_warning) {
    temp_to_warning_ = temp_to_warning;
}
