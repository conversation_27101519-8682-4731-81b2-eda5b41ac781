#include "pmic.h"
#include <esp_timer.h>
#include <esp_sleep.h>
#include <algorithm>

static const char* TAG = "Pmic";

Pmic::Pmic(int charging_pin) : charging_pin_(charging_pin) {
    // 初始化充电状态检测引脚
    gpio_config_t io_conf = {};
    io_conf.intr_type = GPIO_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_INPUT;
    io_conf.pin_bit_mask = (1ULL << charging_pin_);
    io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
    io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
    gpio_config(&io_conf);
    InitializeADC();
}

Pmic::~Pmic() {
    DeInitializeADC();
}

// 初始化ADC
bool Pmic::InitializeADC() {
    if (adc_initialized_) {
        return true;
    }

    // 初始化ADC单元
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = ADC_UNIT_2,
    };
    if (adc_oneshot_new_unit(&init_config, &adc_handle_) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize ADC unit");
        return false;
    }

    // 配置ADC通道
    adc_oneshot_chan_cfg_t ch_config = {
        .atten = BSP_BAT_ADC_ATTEN,
        .bitwidth = ADC_BITWIDTH_DEFAULT,
    };
    if (adc_oneshot_config_channel(adc_handle_, BSP_BAT_ADC_CHAN, &ch_config) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure ADC channel");
        adc_oneshot_del_unit(adc_handle_);
        adc_handle_ = nullptr;
        return false;
    }

    // 初始化校准
    adc_cali_curve_fitting_config_t cali_config = {
        .unit_id = ADC_UNIT_2,
        .chan = BSP_BAT_ADC_CHAN,
        .atten = BSP_BAT_ADC_ATTEN,
        .bitwidth = ADC_BITWIDTH_DEFAULT,
    };
    if (adc_cali_create_scheme_curve_fitting(&cali_config, &cali_handle_) == ESP_OK) {
        adc_initialized_ = true;
        ESP_LOGI(TAG, "ADC initialized successfully");
        return true;
    } else {
        ESP_LOGW(TAG, "ADC calibration failed, using raw values");
        adc_initialized_ = true;  // 即使校准失败也可以使用原始值
        return true;
    }
}

// 反初始化ADC
void Pmic::DeInitializeADC() {
    if (cali_handle_) {
        adc_cali_delete_scheme_curve_fitting(cali_handle_);
        cali_handle_ = nullptr;
    }

    if (adc_handle_) {
        adc_oneshot_del_unit(adc_handle_);
        adc_handle_ = nullptr;
    }

    adc_initialized_ = false;
    ESP_LOGI(TAG, "ADC deinitialized");
}

// 设置电池阈值
void Pmic::SetBatteryThresholds(float low_threshold, float critical_threshold) {
    low_battery_threshold_ = low_threshold;
    critical_battery_threshold_ = critical_threshold;

    ESP_LOGI(TAG, "Battery thresholds set: low=%.2fV, critical=%.2fV",
             low_threshold, critical_threshold);
}

// USB连接检测
bool Pmic::IsUsbConnected() {
    // GPIO18检测充电状态，高电平表示充电中
    return gpio_get_level((gpio_num_t)charging_pin_) == 1;
}

// USB供电检测
bool Pmic::IsUsbPowered() {
    return IsUsbConnected();
}

// 获取电池电压 (mV)
uint16_t Pmic::GetBatteryVoltageMV(void) {
    if (!adc_initialized_) {
        ESP_LOGE(TAG, "Failed to initialize ADC for battery voltage reading");
        return 0;
    }

    int raw_value = 0;
    int voltage = 0; // mV

    // 读取ADC原始值
    esp_err_t ret = adc_oneshot_read(adc_handle_, BSP_BAT_ADC_CHAN, &raw_value);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
        return 0;
    }

    // 转换为电压值
    if (cali_handle_) {
        ret = adc_cali_raw_to_voltage(cali_handle_, raw_value, &voltage);
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "ADC calibration failed, using raw value");
            // 使用简单的线性转换作为备用
            voltage = (raw_value * 1100) / 4095;  // 假设参考电压1.1V，12位ADC
        }
    } else {
        // 没有校准时使用简单转换
        voltage = (raw_value * 1100) / 4095;
    }

    // 应用电压分压比
    voltage = voltage * BSP_BAT_VOL_RATIO;

    ESP_LOGD(TAG, "Battery ADC raw: %d, voltage: %dmV", raw_value, voltage);

    return (uint16_t)voltage;
}

// 电池电量百分比计算方法
uint8_t Pmic::GetBatteryPercent() {
    int voltage = 0;
    for (uint8_t i = 0; i < 10; i++) {
        voltage += GetBatteryVoltageMV();
    }
    voltage /= 10;
    
    // 使用二次函数计算电量百分比
    int percent = (-1 * voltage * voltage + 9016 * voltage - 19189000) / 10000;
    percent = (percent > 100) ? 100 : ((percent < 0) ? 0 : percent);
    
    //ESP_LOGI(TAG, "Battery voltage: %dmV, percentage: %d%%", voltage, percent);
    return (uint8_t)percent;
}

// 获取电池电量等级 (0-100)
int Pmic::GetBatteryLevel() {
    return (int)GetBatteryPercent();
}

// 获取电池电压 (V)
float Pmic::GetBatteryVoltage() {
    return GetBatteryVoltageMV() / 1000.0f;  // 转换为伏特
}

// 充电状态检测
bool Pmic::IsCharging() {
    return IsUsbConnected() && GetBatteryLevel() < 100;
}

// 放电状态检测
bool Pmic::IsDischarging() {
    return !IsCharging();
}

// 充电完成检测
bool Pmic::IsChargingDone() {
    return !IsCharging() && GetBatteryLevel() == 100;
}

// 获取温度 (模拟实现，实际硬件可能需要温度传感器)
float Pmic::GetTemperature() {
    // 这里返回一个模拟温度值
    // 实际实现需要根据具体的温度传感器
    return 25.0f;  // 假设室温25°C
}

// 关机功能 (软件关机)
void Pmic::PowerOff() {
    ESP_LOGI(TAG, "Powering off system...");
    // 这里可以添加关机前的清理工作
    // 对于ESP32，可以进入深度睡眠模式
    esp_deep_sleep_start();
}

// 电池存在检测
bool Pmic::IsBatteryPresent() {
    // 通过检测电池电压来判断电池是否存在
    float battery_voltage = GetBatteryVoltage();
    return battery_voltage > BATTERY_MIN_VOLTAGE && battery_voltage < BATTERY_MAX_VOLTAGE;  // 合理的电池电压范围
}

// 获取电源来源
int Pmic::GetPowerSource() {
    bool usb_connected = IsUsbConnected();
    bool battery_present = IsBatteryPresent();

    if (usb_connected && battery_present) {
        return 2;  // 两者都有
    } else if (usb_connected) {
        return 1;  // USB供电
    } else if (battery_present) {
        return 0;  // 电池供电
    }

    return -1;  // 无电源
}

// 更新电源统计信息
void Pmic::UpdatePowerStats() {
    uint32_t current_time = esp_timer_get_time() / 1000000;  // 转换为秒

    if (last_update_time_ == 0) {
        last_update_time_ = current_time;
        return;
    }

    // 更新运行时间
    uint32_t elapsed_minutes = (current_time - last_update_time_) / 60;
    if (elapsed_minutes > 0) {
        power_stats_.total_runtime_minutes += elapsed_minutes;
        last_update_time_ = current_time;
    }

    // 更新电池电压统计
    float current_voltage = GetBatteryVoltage();
    if (current_voltage > 0) {
        power_stats_.avg_battery_voltage =
            (power_stats_.avg_battery_voltage + current_voltage) / 2.0f;

        if (current_voltage < power_stats_.min_battery_voltage) {
            power_stats_.min_battery_voltage = current_voltage;
        }

        if (current_voltage > power_stats_.max_battery_voltage) {
            power_stats_.max_battery_voltage = current_voltage;
        }

        // 检测低电量事件
        if (current_voltage < low_battery_threshold_) {
            power_stats_.low_battery_events++;
        }
    }

    // 检测充电事件
    static bool last_charging_state = false;
    bool current_charging = IsCharging();
    if (current_charging && !last_charging_state) {
        power_stats_.charging_events++;
        power_stats_.total_charge_cycles++;
    }
    last_charging_state = current_charging;
}

// 电池健康状态检测
bool Pmic::IsBatteryHealthy() {
    float voltage = GetBatteryVoltage();
    float health = GetBatteryHealth();

    return voltage > critical_battery_threshold_ && health > 0.7f;
}

// 获取电池健康度
float Pmic::GetBatteryHealth() {
    float current_voltage = GetBatteryVoltage();

    // 基于电压范围计算健康度
    if (current_voltage < BATTERY_MIN_VOLTAGE) {
        return 0.0f;  // 电池损坏
    } else if (current_voltage > BATTERY_MAX_VOLTAGE) {
        return 0.8f;  // 可能过充，健康度降低
    }

    // 基于电压范围的健康度评估
    float voltage_health = (current_voltage - BATTERY_MIN_VOLTAGE) /
                          (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE);

    // 基于充电循环次数的健康度评估（假设500次循环后开始衰减）
    float cycle_health = 1.0f;
    if (power_stats_.total_charge_cycles > 500) {
        cycle_health = 1.0f - ((power_stats_.total_charge_cycles - 500) * 0.001f);
        cycle_health = std::max(cycle_health, 0.5f);  // 最低50%健康度
    }

    return std::min(voltage_health, cycle_health);
}

// 启用/禁用低功耗模式
void Pmic::EnableLowPowerMode(bool enable) {
    low_power_mode_ = enable;
    ESP_LOGI(TAG, "Low power mode %s", enable ? "enabled" : "disabled");

    if (enable) {
        // 在低功耗模式下可以降低ADC采样频率等
        ESP_LOGI(TAG, "Entering low power mode - reducing monitoring frequency");
    }
}

// 检查是否处于低功耗模式
bool Pmic::IsLowPowerMode() {
    return low_power_mode_;
}

// 获取电源统计信息
Pmic::PowerStats Pmic::GetPowerStats() {
    UpdatePowerStats();  // 确保统计信息是最新的
    return power_stats_;
}

// 重置电源统计信息
void Pmic::ResetPowerStats() {
    power_stats_ = PowerStats();  // 重置为默认值
    last_update_time_ = esp_timer_get_time() / 1000000;
    ESP_LOGI(TAG, "Power statistics reset");
}

