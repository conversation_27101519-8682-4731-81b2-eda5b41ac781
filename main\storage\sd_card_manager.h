#pragma once

#include <sys/stat.h>
#include <sys/unistd.h>
#include <errno.h>
#include <ctime>
#include <cstring>
#include <sdmmc_cmd.h>
#include <driver/sdmmc_host.h>
#include <driver/sdspi_host.h>
#include <esp_vfs_fat.h>
#include <sd_protocol_defs.h>
#include <esp_timer.h>
#include <esp_log.h>
#include "dirent.h"

/**
 * @brief SD卡管理器
 * 支持SD卡的挂载、读写、文件操作等功能
 */
class SDCardManager {
public:
    enum class Status {
        NOT_INITIALIZED,
        INITIALIZING,
        MOUNTED,
        UNMOUNTED,
        ERROR
    };

    struct CardInfo {
        char name[64];
        uint64_t size_bytes;
        uint32_t sector_size;
        uint32_t sector_count;
        uint8_t csd_ver;
        uint8_t mmc_ver;
        float frequency_mhz;
        bool is_mmc;
    };

    struct FileInfo {
        char name[256];
        size_t size;
        bool is_directory;
        time_t modified_time;
    };

    typedef void (*StatusCallback)(Status status, const char* message);
    typedef void (*ProgressCallback)(size_t current, size_t total);

    /**
     * @brief 构造函数
     * @param use_spi_mode 是否使用SPI模式
     */
    SDCardManager(bool use_spi_mode = false);
    /**
     * @brief 析构函数
     */
    ~SDCardManager();

    // 基本操作
    bool Initialize();
    bool Mount(bool format=false);
    bool Unmount();
    bool Remount(); // 强制重新挂载
    bool IsInitialized() const { return status_ != Status::NOT_INITIALIZED; }
    bool IsMounted() const { return status_ == Status::MOUNTED; }
    Status GetStatus() const { return status_; }

    // 卡片信息
    bool GetCardInfo(CardInfo& info) const;
    const char* GetMountPoint() const { return mount_point_; }
    uint64_t GetTotalSize() const;
    uint64_t GetFreeSize() const;
    uint64_t GetUsedSize() const;

    // 文件操作
    bool WriteFile(const char* path, const void* data, size_t size);
    bool ReadFile(const char* path, void* buffer, size_t buffer_size, size_t* bytes_read = nullptr);
    bool AppendFile(const char* path, const void* data, size_t size);
    bool DeleteFile(const char* path);
    bool FileExists(const char* path);
    size_t GetFileSize(const char* path);

    // 文本文件操作
    bool WriteTextFile(const char* path, const char* text);
    bool ReadTextFile(const char* path, char* buffer, size_t buffer_size);
    bool AppendTextFile(const char* path, const char* text);

    // 目录操作
    bool CreateDirectory(const char* path);
    bool DeleteDirectory(const char* path);
    bool ListDirectory(const char* path, FileInfo* files, size_t max_files, size_t* file_count);
    bool DirectoryExists(const char* path);

    // 高级文件操作
    bool CopyFile(const char* src_path, const char* dst_path, ProgressCallback progress = nullptr);
    bool MoveFile(const char* src_path, const char* dst_path);
    bool RenameFile(const char* old_path, const char* new_path);

    // JSON文件操作
    bool WriteJsonFile(const char* path, const char* json_string);
    bool ReadJsonFile(const char* path, char* buffer, size_t buffer_size);

    // 配置文件操作
    bool SaveConfig(const char* filename, const char* key, const char* value);
    bool LoadConfig(const char* filename, const char* key, char* value, size_t value_size);
    bool DeleteConfig(const char* filename, const char* key);

    // 日志文件操作
    bool WriteLog(const char* filename, const char* message, bool with_timestamp = true);
    bool ReadLog(const char* filename, char* buffer, size_t buffer_size, size_t max_lines = 0);
    bool ClearLog(const char* filename);

    // 回调设置
    void SetStatusCallback(StatusCallback callback) { status_callback_ = callback; }

    // 工具函数
    const char* GetStatusName() const;
    const char* GetStatusName(Status s) const;
    void PrintCardInfo() const;
    void PrintDirectoryTree(const char* path = nullptr, int max_depth = 3) const;
    void DiagnoseMountIssues() const;

    bool TryFormatAndRemount();

private:
    // 内部方法
    void UpdateStatus(Status new_status, const char* message = nullptr);
    bool InitializeSDMMC();
    bool InitializeSPI();
    void CleanupResources();
    
    // 路径处理
    void GetFullPath(const char* relative_path, char* full_path, size_t full_path_size) const;
    bool IsValidPath(const char* path) const;

    // 递归创建目录
    bool CreateDirectoryRecursive(const char* full_path);

    // 递归删除目录
    bool DeleteDirectoryRecursive(const char* full_path);

    // 文件系统操作
    bool FormatCard();
    bool CheckFileSystem();

private:
    Status status_;
    sdmmc_card_t* card_;
    sdmmc_host_t host_;
    char mount_point_[32];
    bool use_spi_mode_;
    
    StatusCallback status_callback_;
    
    static const char* TAG;
    static const size_t MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    static const size_t COPY_BUFFER_SIZE = 4096; // 4KB
    friend class SDCardUtils;
};

/**
 * @brief SD卡工具类
 */
class SDCardUtils {
public:
    // 文件格式检测
    static const char* GetFileExtension(const char* filename);
    static bool IsTextFile(const char* filename);
    static bool IsImageFile(const char* filename);
    static bool IsAudioFile(const char* filename);
    static bool IsVideoFile(const char* filename);
    
    // 路径处理
    static void GetFileName(const char* path, char* filename, size_t filename_size);
    static void GetDirectoryPath(const char* path, char* dir_path, size_t dir_path_size);
    static void JoinPath(const char* dir, const char* filename, char* full_path, size_t full_path_size);
    
    // 大小格式化
    static void FormatSize(uint64_t size_bytes, char* buffer, size_t buffer_size);
    static void FormatSpeed(float speed_bps, char* buffer, size_t buffer_size);
    
    // 时间格式化
    static void FormatTime(time_t timestamp, char* buffer, size_t buffer_size);
    
    // 文件校验
    static uint32_t CalculateCRC32(const void* data, size_t size);
    static bool VerifyFileIntegrity(const char* file_path, uint32_t expected_crc);
    
    // 批量操作
    static bool BackupDirectory(SDCardManager& sd, const char* src_dir, const char* backup_dir);
    static bool CleanupOldFiles(SDCardManager& sd, const char* dir, int days_old);
    
    // 示例和测试
    static void ShowSDCardInfo(SDCardManager& sd);
    static void RunFileOperationTests(SDCardManager& sd);
    static void CreateSampleFiles(SDCardManager& sd);
    static void TestDirectoryCreation(SDCardManager& sd);
    static void TestRestoredFunctions(SDCardManager& sd);
    static void TestPinConflictResolution();
};
