{"language": {"type": "tr-TR"}, "strings": {"WARNING": "Uyarı", "INFO": "<PERSON><PERSON><PERSON>", "ERROR": "<PERSON><PERSON>", "VERSION": "S<PERSON>r<PERSON><PERSON> ", "LOADING_PROTOCOL": "<PERSON><PERSON><PERSON><PERSON> bağlanıyor...", "INITIALIZING": "Başlatılıyor...", "PIN_ERROR": "Lütfen SIM kartı takın", "REG_ERROR": "<PERSON><PERSON><PERSON>, veri kartı durumunu kontrol edin", "DETECTING_MODULE": "Mo<PERSON><PERSON>l algılanıyor...", "REGISTERING_NETWORK": "<PERSON><PERSON> be<PERSON>...", "CHECKING_NEW_VERSION": "<PERSON><PERSON> sü<PERSON>üm kontrol ediliyor...", "CHECK_NEW_VERSION_FAILED": "Yeni sürüm kontrolü başarısız, %d saniye sonra tekrar denenecek: %s", "SWITCH_TO_WIFI_NETWORK": "Wi-Fi'ye geçiliyor...", "SWITCH_TO_4G_NETWORK": "4G'ye geçiliyor...", "STANDBY": "Bekleme", "CONNECT_TO": "Bağlan ", "CONNECTING": "Bağlanıyor...", "CONNECTED_TO": "Bağlandı ", "LISTENING": "Dinleniyor...", "SPEAKING": "Konuşuluyor...", "SERVER_NOT_FOUND": "Mevcut hizmet aranıyor", "SERVER_NOT_CONNECTED": "<PERSON>z<PERSON>e b<PERSON><PERSON><PERSON>r, lüt<PERSON> daha sonra deneyin", "SERVER_TIMEOUT": "<PERSON><PERSON>t zaman aşımı", "SERVER_ERROR": "G<PERSON>nderme başar<PERSON>s<PERSON>z, ağı kontrol edin", "CONNECT_TO_HOTSPOT": "Telefonu hotspot'a bağlayın ", "ACCESS_VIA_BROWSER": "，tarayıcı üzerinden erişin ", "WIFI_CONFIG_MODE": "<PERSON><PERSON>ılandırma modu", "ENTERING_WIFI_CONFIG_MODE": "<PERSON><PERSON> yapılandırma moduna giriliyor...", "SCANNING_WIFI": "Wi-Fi taranıyor...", "NEW_VERSION": "<PERSON><PERSON> ", "OTA_UPGRADE": "OTA güncelleme", "UPGRADING": "Sistem güncelleniyor...", "UPGRADE_FAILED": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarı<PERSON><PERSON>z", "ACTIVATION": "Cihaz aktivasyonu", "BATTERY_LOW": "<PERSON><PERSON>", "BATTERY_CHARGING": "<PERSON>arj <PERSON>", "BATTERY_FULL": "<PERSON><PERSON> dolu", "BATTERY_NEED_CHARGE": "<PERSON><PERSON>, lütfen şarj edin", "VOLUME": "Ses ", "MUTED": "<PERSON><PERSON><PERSON>", "MAX_VOLUME": "<PERSON><PERSON><PERSON><PERSON> ses", "RTC_MODE_OFF": "AEC kapalı", "RTC_MODE_ON": "AEC açık"}}