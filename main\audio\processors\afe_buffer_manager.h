#ifndef AFE_BUFFER_MANAGER_H
#define AFE_BUFFER_MANAGER_H

#include <esp_afe_sr_iface.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>
#include <freertos/semphr.h>
#include <atomic>
#include <chrono>
#include <functional>

/**
 * @brief AFE缓冲区管理器
 * 
 * 解决AFE环形缓冲区满的问题，提供：
 * - 智能缓冲区监控
 * - 自适应处理频率
 * - 缓冲区溢出保护
 * - 性能统计和调优
 */
class AfeBufferManager {
public:
    struct BufferStats {
        uint32_t total_feeds = 0;           // 总feed次数
        uint32_t total_fetches = 0;         // 总fetch次数
        uint32_t buffer_overflows = 0;      // 缓冲区溢出次数
        uint32_t fetch_failures = 0;       // fetch失败次数
        uint32_t avg_fetch_time_us = 0;     // 平均fetch时间(微秒)
        uint32_t max_fetch_time_us = 0;     // 最大fetch时间(微秒)
        uint32_t buffer_usage_percent = 0;  // 缓冲区使用率
        bool is_healthy = true;             // 缓冲区健康状态
    };

    enum class BufferState {
        NORMAL,         // 正常状态
        HIGH_USAGE,     // 高使用率
        NEAR_FULL,      // 接近满
        OVERFLOW,       // 溢出
        ERROR           // 错误状态
    };

    AfeBufferManager();
    ~AfeBufferManager();

    // 初始化和配置
    bool Initialize(esp_afe_sr_iface_t* afe_iface, esp_afe_sr_data_t* afe_data);
    void SetBufferThresholds(float high_usage_threshold = 0.7f, float near_full_threshold = 0.9f);
    void SetFetchTimeout(uint32_t timeout_ms = 100);
    void SetMonitoringInterval(uint32_t interval_ms = 50);

    // 缓冲区操作
    bool SafeFeed(const int16_t* data);
    bool SafeFetch(afe_fetch_result_t** result, uint32_t timeout_ms = 0);
    bool ForceFlushBuffer();
    bool ResetBuffer();

    // 监控和统计
    BufferStats GetStats() const;
    BufferState GetBufferState() const;
    float GetBufferUsagePercent() const;
    bool IsBufferHealthy() const;

    // 自适应优化
    void EnableAdaptiveProcessing(bool enable = true);
    void SetProcessingPriority(UBaseType_t priority);
    void OptimizeForLowLatency();
    void OptimizeForStability();

    // 回调设置
    void OnBufferOverflow(std::function<void(const BufferStats&)> callback);
    void OnBufferStateChange(std::function<void(BufferState, BufferState)> callback);
    void OnPerformanceAlert(std::function<void(const std::string&)> callback);

    // 控制方法
    void StartMonitoring();
    void StopMonitoring();
    bool IsMonitoring() const;

private:
    esp_afe_sr_iface_t* afe_iface_;
    esp_afe_sr_data_t* afe_data_;
    
    // 配置参数
    float high_usage_threshold_;
    float near_full_threshold_;
    uint32_t fetch_timeout_ms_;
    uint32_t monitoring_interval_ms_;
    bool adaptive_processing_enabled_;
    UBaseType_t processing_priority_;

    // 状态管理
    std::atomic<BufferState> current_state_;
    std::atomic<bool> monitoring_active_;
    std::atomic<bool> force_stop_;
    
    // 统计数据
    mutable SemaphoreHandle_t stats_mutex_;
    BufferStats stats_;
    std::chrono::steady_clock::time_point last_stats_update_;

    // FreeRTOS对象
    TaskHandle_t monitor_task_handle_;
    EventGroupHandle_t event_group_;
    
    // 事件位定义
    static const EventBits_t MONITOR_START_BIT = BIT0;
    static const EventBits_t MONITOR_STOP_BIT = BIT1;
    static const EventBits_t BUFFER_OVERFLOW_BIT = BIT2;

    // 回调函数
    std::function<void(const BufferStats&)> overflow_callback_;
    std::function<void(BufferState, BufferState)> state_change_callback_;
    std::function<void(const std::string&)> performance_alert_callback_;

    // 内部方法
    static void MonitorTaskWrapper(void* param);
    void MonitorTask();
    void UpdateStats();
    void CheckBufferHealth();
    void HandleBufferOverflow();
    void AdjustProcessingStrategy();
    BufferState DetermineBufferState() const;
    void NotifyStateChange(BufferState new_state);
    uint32_t EstimateBufferUsage() const;
    
    static const char* TAG;
};

/**
 * @brief AFE缓冲区管理器工厂类
 */
class AfeBufferManagerFactory {
public:
    // 创建针对不同场景优化的管理器
    static std::unique_ptr<AfeBufferManager> CreateForWakeWord();
    static std::unique_ptr<AfeBufferManager> CreateForVoiceProcessing();
    static std::unique_ptr<AfeBufferManager> CreateForLowLatency();
    static std::unique_ptr<AfeBufferManager> CreateForStability();

    // 根据硬件配置创建
    static std::unique_ptr<AfeBufferManager> CreateForHardware(
        size_t available_ram_kb,
        uint32_t cpu_freq_mhz,
        bool has_psram = false
    );
};

/**
 * @brief AFE性能调优助手
 */
class AfePerformanceTuner {
public:
    struct TuningRecommendation {
        std::string description;
        std::string action;
        int priority;  // 1-10, 10最高
        bool critical;
    };

    static std::vector<TuningRecommendation> AnalyzePerformance(
        const AfeBufferManager::BufferStats& stats
    );
    
    static void ApplyRecommendations(
        AfeBufferManager& manager,
        const std::vector<TuningRecommendation>& recommendations
    );
    
    static std::string GeneratePerformanceReport(
        const AfeBufferManager::BufferStats& stats
    );
};

#endif // AFE_BUFFER_MANAGER_H
