#include "ble_manager.h"

#ifdef CONFIG_BT_ENABLED

#include <nvs_flash.h>
#include <string.h>
#include <stdlib.h>

const char* BleManager::TAG = "BleManager";
BleManager* BleManager::instance_ = nullptr;

BleManager::BleManager()
    : current_state_(State::DISABLED)
    , is_initialized_(false)
    , gatts_if_(ESP_GATT_IF_NONE)
    , conn_id_(0)
    , service_handle_(0)
    , char_handle_(0)
    , descr_handle_(0)
    , state_callback_(nullptr)
    , device_callback_(nullptr)
    , data_callback_(nullptr)
{
    instance_ = this;
    memset(&connected_device_, 0, sizeof(connected_device_));
}

BleManager::~BleManager() {
    Disable();
    instance_ = nullptr;
}

bool BleManager::Initialize() {
    if (is_initialized_) {
        return true;
    }

    ESP_LOGI(TAG, "Initializing BLE without security (basic mode)");

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 释放Classic Bluetooth内存
    ESP_ERROR_CHECK(esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT));

    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return false;
    }

    // 启用BLE模式
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return false;
    }

    // 初始化蓝牙栈
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bluedroid: %s", esp_err_to_name(ret));
        return false;
    }

    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable bluedroid: %s", esp_err_to_name(ret));
        return false;
    }

    // 注册回调
    ret = esp_ble_gap_register_callback(GapEventHandler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return false;
    }

    ret = esp_ble_gatts_register_callback(GattsEventHandler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATTS callback: %s", esp_err_to_name(ret));
        return false;
    }

    // 注册GATT应用
    ret = esp_ble_gatts_app_register(0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATT app: %s", esp_err_to_name(ret));
        return false;
    }

    // 重要：完全跳过安全配置
    ESP_LOGI(TAG, "✅ Skipping all security configuration");
    ESP_LOGI(TAG, "✅ BLE initialized in basic mode (no security)");

    is_initialized_ = true;
    UpdateState(State::IDLE);

    return true;
}

bool BleManager::Enable() {
    return Initialize();
}

bool BleManager::Disable() {
    if (!is_initialized_) {
        return true;
    }

    ESP_LOGI(TAG, "Disabling BLE");

    StopAdvertising();
    StopScanning();

    esp_bluedroid_disable();
    esp_bluedroid_deinit();
    esp_bt_controller_disable();
    esp_bt_controller_deinit();

    is_initialized_ = false;
    UpdateState(State::DISABLED);

    ESP_LOGI(TAG, "BLE disabled");
    return true;
}

bool BleManager::StartAdvertising() {
    if (current_state_ != State::IDLE) {
        ESP_LOGW(TAG, "Cannot start advertising in current state: %s", GetBleStateName(current_state_));
        return false;
    }

    ESP_LOGI(TAG, "Starting BLE advertising (no security mode)");

    // 首先设置广播数据
    if (!SetupBasicAdvertisingData()) {
        ESP_LOGE(TAG, "Failed to setup advertising data");
        return false;
    }

    // 设置广播参数
    esp_ble_adv_params_t adv_params = {};
    adv_params.adv_int_min = 0x20;
    adv_params.adv_int_max = 0x40;
    adv_params.adv_type = ADV_TYPE_IND;
    adv_params.own_addr_type = BLE_ADDR_TYPE_PUBLIC;
    adv_params.channel_map = ADV_CHNL_ALL;
    adv_params.adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY;

    esp_err_t ret = esp_ble_gap_start_advertising(&adv_params);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start advertising: %s", esp_err_to_name(ret));
        return false;
    }

    UpdateState(State::ADVERTISING);
    return true;
}

bool BleManager::StopAdvertising() {
    if (current_state_ != State::ADVERTISING) {
        return true;
    }

    ESP_LOGI(TAG, "Stopping BLE advertising");
    esp_err_t ret = esp_ble_gap_stop_advertising();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop advertising: %s", esp_err_to_name(ret));
        return false;
    }

    UpdateState(State::IDLE);
    return true;
}

bool BleManager::StartScanning(int duration_sec) {
    if (current_state_ != State::IDLE) {
        ESP_LOGW(TAG, "Cannot start scanning in current state: %s", GetBleStateName(current_state_));
        return false;
    }

    ESP_LOGI(TAG, "Starting BLE scanning for %d seconds", duration_sec);

    esp_ble_scan_params_t scan_params = {};
    scan_params.scan_type = BLE_SCAN_TYPE_ACTIVE;
    scan_params.own_addr_type = BLE_ADDR_TYPE_PUBLIC;
    scan_params.scan_filter_policy = BLE_SCAN_FILTER_ALLOW_ALL;
    scan_params.scan_interval = 0x50;
    scan_params.scan_window = 0x30;
    scan_params.scan_duplicate = BLE_SCAN_DUPLICATE_DISABLE;

    esp_err_t ret = esp_ble_gap_set_scan_params(&scan_params);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set scan params: %s", esp_err_to_name(ret));
        return false;
    }

    ret = esp_ble_gap_start_scanning(duration_sec);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start scanning: %s", esp_err_to_name(ret));
        return false;
    }

    UpdateState(State::SCANNING);
    return true;
}

bool BleManager::StopScanning() {
    if (current_state_ != State::SCANNING) {
        return true;
    }

    ESP_LOGI(TAG, "Stopping BLE scanning");
    esp_err_t ret = esp_ble_gap_stop_scanning();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop scanning: %s", esp_err_to_name(ret));
        return false;
    }

    UpdateState(State::IDLE);
    return true;
}

void BleManager::SetDeviceName(const char* name) {
    if (!is_initialized_) {
        ESP_LOGW(TAG, "BLE not initialized");
        return;
    }

    ESP_LOGI(TAG, "Setting device name to: %s", name);
    esp_ble_gap_set_device_name(name);
}

bool BleManager::SendNotification(const uint8_t* data, size_t len) {
    if (current_state_ != State::CONNECTED || char_handle_ == 0) {
        ESP_LOGW(TAG, "Cannot send notification - not connected or service not ready");
        return false;
    }

    ESP_LOGI(TAG, "Sending notification, len: %d", len);
    ESP_LOG_BUFFER_HEX(TAG, data, len);

    esp_err_t ret = esp_ble_gatts_send_indicate(gatts_if_, conn_id_, char_handle_,
                                               len, (uint8_t*)data, false);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send notification: %s", esp_err_to_name(ret));
        return false;
    }

    return true;
}

bool BleManager::SendNotification(const char* text) {
    return SendNotification((const uint8_t*)text, strlen(text));
}

bool BleManager::SetupBasicAdvertisingData() {
    ESP_LOGI(TAG, "Setting up basic advertising data (no security)");

    esp_ble_adv_data_t adv_data = {};
    adv_data.set_scan_rsp = false;
    adv_data.include_name = true;
    adv_data.include_txpower = true;
    adv_data.min_interval = 0x0006;
    adv_data.max_interval = 0x0010;
    adv_data.appearance = 0x00;
    adv_data.manufacturer_len = 0;
    adv_data.p_manufacturer_data = nullptr;
    adv_data.service_data_len = 0;
    adv_data.p_service_data = nullptr;
    adv_data.service_uuid_len = 0;
    adv_data.p_service_uuid = nullptr;
    adv_data.flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT);

    esp_err_t ret = esp_ble_gap_config_adv_data(&adv_data);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to config advertising data: %s", esp_err_to_name(ret));
        return false;
    }

    ESP_LOGI(TAG, "Basic advertising data configured successfully");
    return true;
}

bool BleManager::CreateGattService() {
    ESP_LOGI(TAG, "Creating custom GATT service");

    // 定义自定义服务UUID (16字节)
    static const uint8_t service_uuid[16] = {
        0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
        0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0
    };

    // 创建服务ID
    esp_gatt_srvc_id_t service_id;
    service_id.is_primary = true;
    service_id.id.inst_id = 0x00;
    service_id.id.uuid.len = ESP_UUID_LEN_128;
    memcpy(service_id.id.uuid.uuid.uuid128, service_uuid, ESP_UUID_LEN_128);

    // 创建服务 (需要4个句柄：服务 + 特征 + 特征值 + 描述符)
    esp_err_t ret = esp_ble_gatts_create_service(gatts_if_, &service_id, 4);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create GATT service: %s", esp_err_to_name(ret));
        return false;
    }

    ESP_LOGI(TAG, "GATT service creation initiated");
    return true;
}

void BleManager::UpdateState(State new_state) {
    if (current_state_ != new_state) {
        State old_state = current_state_;
        current_state_ = new_state;
        
        ESP_LOGI(TAG, "State changed: %s -> %s", 
                GetBleStateName(old_state), 
                GetBleStateName(new_state));
        
        if (state_callback_) {
            state_callback_(new_state);
        }
    }
}

// 静态回调函数
void BleManager::GapEventHandler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGapEvent(event, param);
    }
}

void BleManager::GattsEventHandler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGattsEvent(event, gatts_if, param);
    }
}

#endif

void BleManager::HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    switch (event) {
        case ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT:
            ESP_LOGI(TAG, "Advertising data set complete");
            break;

        case ESP_GAP_BLE_SCAN_RESULT_EVT: {
            if (param->scan_rst.search_evt == ESP_GAP_SEARCH_INQ_RES_EVT) {
                Device device = {};
                memcpy(device.address, param->scan_rst.bda, sizeof(esp_bd_addr_t));
                device.rssi = param->scan_rst.rssi;

                uint8_t* adv_name = nullptr;
                uint8_t adv_name_len = 0;
                adv_name = esp_ble_resolve_adv_data(param->scan_rst.ble_adv,
                                                   ESP_BLE_AD_TYPE_NAME_CMPL, &adv_name_len);
                if (adv_name && adv_name_len < sizeof(device.name)) {
                    memcpy(device.name, adv_name, adv_name_len);
                    device.name[adv_name_len] = '\0';
                } else {
                    strcpy(device.name, "Unknown");
                }

                ESP_LOGI(TAG, "Found device: %s RSSI: %d", device.name, device.rssi);

                if (device_callback_) {
                    device_callback_(device);
                }
            } else if (param->scan_rst.search_evt == ESP_GAP_SEARCH_INQ_CMPL_EVT) {
                ESP_LOGI(TAG, "Scan complete");
                UpdateState(State::IDLE);
            }
            break;
        }

        case ESP_GAP_BLE_ADV_START_COMPLETE_EVT:
            if (param->adv_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "✅ Advertising started successfully (no security mode)");
                ESP_LOGI(TAG, "Device should now be discoverable as 'XiaoZhi-ESP32'");
            } else {
                ESP_LOGE(TAG, "❌ Advertising start failed");
                UpdateState(State::IDLE);
            }
            break;

        case ESP_GAP_BLE_ADV_STOP_COMPLETE_EVT:
            ESP_LOGI(TAG, "Advertising stopped");
            UpdateState(State::IDLE);
            break;

        // 完全忽略所有安全相关事件
        case ESP_GAP_BLE_SEC_REQ_EVT:
        case ESP_GAP_BLE_PASSKEY_REQ_EVT:
        case ESP_GAP_BLE_OOB_REQ_EVT:
        case ESP_GAP_BLE_LOCAL_IR_EVT:
        case ESP_GAP_BLE_LOCAL_ER_EVT:
        case ESP_GAP_BLE_NC_REQ_EVT:
        case ESP_GAP_BLE_AUTH_CMPL_EVT:
        case ESP_GAP_BLE_KEY_EVT:
            ESP_LOGI(TAG, "Security event %d - completely ignored", event);
            // 完全不处理任何安全事件
            break;

        default:
            ESP_LOGD(TAG, "GAP event: %d", event);
            break;
    }
}

void BleManager::HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) {
    switch (event) {
        case ESP_GATTS_REG_EVT:
            ESP_LOGI(TAG, "GATTS app registered (no security mode)");
            gatts_if_ = gatts_if;
            esp_ble_gap_set_device_name("XiaoZhi-ESP32");

            // 创建GATT服务
            CreateGattService();
            break;

        case ESP_GATTS_CREATE_EVT: {
            ESP_LOGI(TAG, "GATT service created, service_handle: %d", param->create.service_handle);
            service_handle_ = param->create.service_handle;

            // 启动服务
            esp_ble_gatts_start_service(service_handle_);

            // 添加特征
            esp_bt_uuid_t char_uuid;
            char_uuid.len = ESP_UUID_LEN_128;
            uint8_t char_uuid_128[16] = {
                0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf1,
                0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf1
            };
            memcpy(char_uuid.uuid.uuid128, char_uuid_128, ESP_UUID_LEN_128);

            esp_err_t ret = esp_ble_gatts_add_char(service_handle_, &char_uuid,
                                                  ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                                                  ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_NOTIFY,
                                                  nullptr, nullptr);
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "Failed to add characteristic: %s", esp_err_to_name(ret));
            }
            break;
        }

        case ESP_GATTS_ADD_CHAR_EVT: {
            ESP_LOGI(TAG, "Characteristic added, char_handle: %d", param->add_char.attr_handle);
            char_handle_ = param->add_char.attr_handle;

            // 添加特征描述符 (Client Characteristic Configuration)
            esp_bt_uuid_t descr_uuid;
            descr_uuid.len = ESP_UUID_LEN_16;
            descr_uuid.uuid.uuid16 = ESP_GATT_UUID_CHAR_CLIENT_CONFIG;

            // 创建描述符属性值结构（使用静态数组避免内存管理）
            static uint8_t descr_value[2] = {0x00, 0x00};
            esp_attr_value_t descr_val;
            descr_val.attr_max_len = 2;
            descr_val.attr_len = 2;
            descr_val.attr_value = descr_value;

            esp_err_t ret2 = esp_ble_gatts_add_char_descr(service_handle_, &descr_uuid,
                                                         ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                                                         &descr_val, nullptr);
            if (ret2 != ESP_OK) {
                ESP_LOGE(TAG, "Failed to add descriptor: %s", esp_err_to_name(ret2));
            }
            break;
        }

        case ESP_GATTS_ADD_CHAR_DESCR_EVT:
            ESP_LOGI(TAG, "Descriptor added, descr_handle: %d", param->add_char_descr.attr_handle);
            descr_handle_ = param->add_char_descr.attr_handle;
            ESP_LOGI(TAG, "✅ GATT service setup complete!");
            ESP_LOGI(TAG, "   Service Handle: %d", service_handle_);
            ESP_LOGI(TAG, "   Characteristic Handle: %d", char_handle_);
            ESP_LOGI(TAG, "   Descriptor Handle: %d", descr_handle_);
            break;

        case ESP_GATTS_START_EVT:
            ESP_LOGI(TAG, "GATT service started successfully");
            break;

        case ESP_GATTS_CONNECT_EVT: {
            ESP_LOGI(TAG, "✅ Device connected successfully (no security required)!");
            ESP_LOGI(TAG, "   Connection ID: %d", param->connect.conn_id);
            ESP_LOGI(TAG, "   Remote Address: %02x:%02x:%02x:%02x:%02x:%02x",
                    param->connect.remote_bda[0], param->connect.remote_bda[1],
                    param->connect.remote_bda[2], param->connect.remote_bda[3],
                    param->connect.remote_bda[4], param->connect.remote_bda[5]);

            conn_id_ = param->connect.conn_id;
            memcpy(connected_device_.address, param->connect.remote_bda, sizeof(esp_bd_addr_t));
            connected_device_.is_connected = true;

            UpdateState(State::CONNECTED);

            ESP_LOGI(TAG, "🎉 Connection established without any security requirements");
            break;
        }

        case ESP_GATTS_DISCONNECT_EVT:
            ESP_LOGI(TAG, "❌ Device disconnected");
            ESP_LOGI(TAG, "   Connection ID: %d", param->disconnect.conn_id);
            ESP_LOGI(TAG, "   Reason: 0x%02x", param->disconnect.reason);

            connected_device_.is_connected = false;
            UpdateState(State::IDLE);

            ESP_LOGI(TAG, "Restarting advertising after disconnect");
            StartAdvertising();
            break;

        case ESP_GATTS_READ_EVT: {
            ESP_LOGI(TAG, "GATTS read event - handle: %d", param->read.handle);

            // 发送读响应
            esp_gatt_rsp_t rsp;
            memset(&rsp, 0, sizeof(esp_gatt_rsp_t));
            rsp.attr_value.handle = param->read.handle;

            if (param->read.handle == char_handle_) {
                // 返回示例数据
                const char* response = "Hello from XiaoZhi-ESP32!";
                rsp.attr_value.len = strlen(response);
                memcpy(rsp.attr_value.value, response, rsp.attr_value.len);
                ESP_LOGI(TAG, "Sending read response: %s", response);
            }

            esp_ble_gatts_send_response(gatts_if_, param->read.conn_id, param->read.trans_id,
                                       ESP_GATT_OK, &rsp);
            break;
        }

        case ESP_GATTS_WRITE_EVT: {
            ESP_LOGI(TAG, "GATTS write event - handle: %d, len: %d",
                    param->write.handle, param->write.len);

            if (param->write.handle == char_handle_) {
                ESP_LOGI(TAG, "Data received on characteristic:");
                ESP_LOG_BUFFER_HEX(TAG, param->write.value, param->write.len);

                // 如果数据是文本，也打印为字符串
                if (param->write.len > 0) {
                    char text_data[256];
                    size_t copy_len = param->write.len < sizeof(text_data) - 1 ? param->write.len : sizeof(text_data) - 1;
                    memcpy(text_data, param->write.value, copy_len);
                    text_data[copy_len] = '\0';
                    ESP_LOGI(TAG, "Data as text: %s", text_data);
                }

                if (data_callback_) {
                    data_callback_(param->write.value, param->write.len);
                }

                // 发送写响应（如果需要）
                if (param->write.need_rsp) {
                    esp_ble_gatts_send_response(gatts_if_, param->write.conn_id, param->write.trans_id,
                                               ESP_GATT_OK, nullptr);
                }
            }
            break;
        }

        default:
            ESP_LOGD(TAG, "GATTS event: %d", event);
            break;
    }
}

const char* GetBleStateName(BleManager::State state) {
    switch (state) {
        case BleManager::State::DISABLED: return "DISABLED";
        case BleManager::State::IDLE: return "IDLE";
        case BleManager::State::ADVERTISING: return "ADVERTISING";
        case BleManager::State::SCANNING: return "SCANNING";
        case BleManager::State::CONNECTED: return "CONNECTED";
        default: return "UNKNOWN";
    }
}
