#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_mac.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#if CONFIG_BT_CONTROLLER_ENABLED || !CONFIG_BT_NIMBLE_ENABLED
#include "esp_bt.h"
#endif

#include "esp_blufi_api.h"
#include "esp_blufi.h"
#include "blufi_manager.h"
#include "wifi_configuration_ap.h"

#define CONNECTED_BIT BIT0

#ifdef CONFIG_BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY
#define BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY CONFIG_BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY
#else
#define BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY 5
#endif
#define BLUFI_INVALID_REASON                255
#define BLUFI_INVALID_RSSI                  -128

#if CONFIG_ESP_WIFI_AUTH_OPEN
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_OPEN
#elif CONFIG_ESP_WIFI_AUTH_WEP
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WEP
#elif CONFIG_ESP_WIFI_AUTH_WPA_PSK
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WPA_PSK
#elif CONFIG_ESP_WIFI_AUTH_WPA2_PSK
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WPA2_PSK
#elif CONFIG_ESP_WIFI_AUTH_WPA_WPA2_PSK
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WPA_WPA2_PSK
#elif CONFIG_ESP_WIFI_AUTH_WPA3_PSK
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WPA3_PSK
#elif CONFIG_ESP_WIFI_AUTH_WPA2_WPA3_PSK
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WPA2_WPA3_PSK
#elif CONFIG_ESP_WIFI_AUTH_WAPI_PSK
#define BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD WIFI_AUTH_WAPI_PSK
#endif

const char* BluFiManager::TAG = "BluFiManager";
BluFiManager* BluFiManager::instance_ = nullptr;

BluFiManager::BluFiManager()
    : current_state_(State::DISABLED)
    , is_initialized_(false)
    , state_callback_(nullptr)
    , wifi_config_callback_(nullptr)
    , status_callback_(nullptr)
{
    instance_ = this;
    memset(&wifi_config_, 0, sizeof(wifi_config_));
}

BluFiManager::~BluFiManager() {
    Disable();
    instance_ = nullptr;
}

bool BluFiManager::Initialize() {
    if (is_initialized_) {
        return true;
    }

    ESP_LOGI(TAG, "Initializing BluFi");

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    InitialiseWifi();

#if CONFIG_BT_CONTROLLER_ENABLED || !CONFIG_BT_NIMBLE_ENABLED
    ret = esp_blufi_controller_init();
    if (ret) {
        BLUFI_ERROR("%s BLUFI controller init failed: %s\n", __func__, esp_err_to_name(ret));
        return false;
    }
#endif

    // 初始化BluFi
    esp_blufi_callbacks_t blufi_callbacks = {
        .event_cb = BluFiEventCallback,
        .negotiate_data_handler = blufi_dh_negotiate_data_handler,
        .encrypt_func = blufi_aes_encrypt,
        .decrypt_func = blufi_aes_decrypt,
        .checksum_func = blufi_crc_checksum,
    };
    ret = esp_blufi_host_and_cb_init(&blufi_callbacks);
    if (ret) {
        BLUFI_ERROR("%s initialise failed: %s\n", __func__, esp_err_to_name(ret));
        return false;
    }

    BLUFI_INFO("BLUFI VERSION %04x\n", esp_blufi_get_version());

    is_initialized_ = true;
    UpdateState(State::IDLE, nullptr);

    ESP_LOGI(TAG, "✅ BluFi initialized successfully");
    return true;
}

bool BluFiManager::Enable() {
    return Initialize();
}

bool BluFiManager::Disable() {
    if (!is_initialized_) {
        return true;
    }

    ESP_LOGI(TAG, "Disabling BluFi");

    esp_blufi_controller_deinit();

    is_initialized_ = false;
    UpdateState(State::DISABLED, nullptr);

    ESP_LOGI(TAG, "BluFi disabled");
    return true;
}

void BluFiManager::ReportWifiStatus(wifi_event_t event) {
    if (!is_initialized_) {
        return;
    }

    ESP_LOGI(TAG, "Reporting WiFi status: %d", event);
    
    switch (event) {
        case WIFI_EVENT_STA_START:
            esp_blufi_send_wifi_conn_report(WIFI_MODE_STA, ESP_BLUFI_STA_CONN_SUCCESS, 0, nullptr);
            break;
        case WIFI_EVENT_STA_CONNECTED:
            esp_blufi_send_wifi_conn_report(WIFI_MODE_STA, ESP_BLUFI_STA_CONN_SUCCESS, 0, nullptr);
            break;
        case WIFI_EVENT_STA_DISCONNECTED:
            esp_blufi_send_wifi_conn_report(WIFI_MODE_STA, ESP_BLUFI_STA_CONN_FAIL, 0, nullptr);
            break;
        default:
            break;
    }
}

void BluFiManager::ReportWifiConnected(const char* ip) {
    if (!is_initialized_) {
        return;
    }

    ESP_LOGI(TAG, "Reporting WiFi connected with IP: %s", ip);
    
    // 发送IP地址信息
    esp_blufi_extra_info_t info;
    memset(&info, 0, sizeof(esp_blufi_extra_info_t));
    memcpy(info.sta_bssid, ip, strlen(ip) < sizeof(info.sta_bssid) ? strlen(ip) : sizeof(info.sta_bssid));
    info.sta_bssid_set = true;
    
    esp_blufi_send_wifi_conn_report(WIFI_MODE_STA, ESP_BLUFI_STA_CONN_SUCCESS, 0, &info);

    auto& wifi_ap = WifiConfigurationAp::GetInstance();
    wifi_ap.Save(wifi_config_.ssid, wifi_config_.password);
    
    char msg[64];
    snprintf(msg, sizeof(msg), "WiFi connected: %s", ip);
    UpdateState(State::CONNECTED_TO_WIFI, msg);
}

void BluFiManager::ReportWifiDisconnected() {
    if (!is_initialized_) {
        return;
    }

    ESP_LOGI(TAG, "Reporting WiFi disconnected");
    esp_blufi_send_wifi_conn_report(WIFI_MODE_STA, ESP_BLUFI_STA_CONN_FAIL, 0, nullptr);
    
    UpdateState(State::IDLE, "WiFi disconnected");
}

void BluFiManager::UpdateState(State new_state, const char * msg) {
    if (current_state_ != new_state) {
        State old_state = current_state_;
        current_state_ = new_state;
        
        ESP_LOGI(TAG, "State changed: %s -> %s", 
                GetBluFiStateName(old_state), 
                GetBluFiStateName(new_state));
        
        if (state_callback_) {
            state_callback_(new_state, is_initialized_);
        }
        if (status_callback_ && msg) {
            status_callback_(msg);
        }
    }
}

void BluFiManager::UpdateWifiConfig(const char* ssid, size_t ssid_len, const char* password, size_t pass_len) {
    if (ssid) {
        strncpy(wifi_config_.ssid, ssid, ssid_len);
        wifi_config_.ssid[ssid_len] = '\0';
    }
    
    if (password) {
        strncpy(wifi_config_.password, password, pass_len);
        wifi_config_.password[pass_len] = '\0';
    }
    
    wifi_config_.is_configured = (strlen(wifi_config_.ssid) > 0) && (strlen(wifi_config_.password) > 0);
    
    ESP_LOGI(TAG, "WiFi config updated - SSID: %s, Passoword: %s", wifi_config_.ssid, wifi_config_.password);
    
    if (wifi_config_callback_ && wifi_config_.is_configured) {
        wifi_config_callback_(wifi_config_);
    }
}

int BluFiManager::SoftapGetCurrentConnectionNumber(void)
{
    esp_err_t ret;
    wifi_sta_list_t gl_sta_list;
    ret = esp_wifi_ap_get_sta_list(&gl_sta_list);
    if (ret == ESP_OK)
    {
        return gl_sta_list.num;
    }

    return 0;
}

void BluFiManager::IpEventHandler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data)
{
    wifi_mode_t mode;
    BluFiManager* instance = static_cast<BluFiManager*>(arg);

    switch (event_id) {
    case IP_EVENT_STA_GOT_IP: {
        esp_blufi_extra_info_t info;

        xEventGroupSetBits(instance->wifi_event_group_, CONNECTED_BIT);
        esp_wifi_get_mode(&mode);

        memset(&info, 0, sizeof(esp_blufi_extra_info_t));
        memcpy(info.sta_bssid, instance->gl_sta_bssid_, 6);
        info.sta_bssid_set = true;
        info.sta_ssid = instance->gl_sta_ssid;
        info.sta_ssid_len = instance->gl_sta_ssid_len_;
        instance->gl_sta_got_ip_ = true;
        if (instance->ble_is_connected_ == true) {
            esp_blufi_send_wifi_conn_report(mode, ESP_BLUFI_STA_CONN_SUCCESS, instance->SoftapGetCurrentConnectionNumber(), &info);
        } else {
            BLUFI_INFO("BLUFI BLE is not connected yet\n");
        }

        auto* event = static_cast<ip_event_got_ip_t*>(event_data);
        char ip_address[16];
        esp_ip4addr_ntoa(&event->ip_info.ip, ip_address, sizeof(ip_address));
        ESP_LOGI(TAG, "Got IP: %s", ip_address);
        instance->ReportWifiConnected(ip_address);

        break;
    }
    default:
        break;
    }
    return;
}

void BluFiManager::WifiEventHandler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data)
{
    wifi_event_sta_connected_t *event;
    wifi_event_sta_disconnected_t *disconnected_event;
    wifi_mode_t mode;
    BluFiManager* instance = static_cast<BluFiManager*>(arg);

    switch (event_id) {
    case WIFI_EVENT_STA_START:
        instance->BlufiWifiConnect();
        break;
    case WIFI_EVENT_STA_CONNECTED:
        instance->gl_sta_connected_ = true;
        instance->gl_sta_is_connecting_ = false;
        event = (wifi_event_sta_connected_t*) event_data;
        memcpy(instance->gl_sta_bssid_, event->bssid, 6);
        memcpy(instance->gl_sta_ssid, event->ssid, event->ssid_len);
        instance->gl_sta_ssid_len_ = event->ssid_len;
        break;
    case WIFI_EVENT_STA_DISCONNECTED:
        /* Only handle reconnection during connecting */
        if (instance->gl_sta_connected_ == false && instance->BlufiWifiReconnect() == false) {
            instance->gl_sta_is_connecting_ = false;
            disconnected_event = (wifi_event_sta_disconnected_t*) event_data;
            instance->BlufiRecordWifiConnInfo(disconnected_event->rssi, disconnected_event->reason);
            instance->ReportWifiDisconnected();
        }
        /* This is a workaround as ESP32 WiFi libs don't currently
           auto-reassociate. */
        instance->gl_sta_connected_ = false;
        instance->gl_sta_got_ip_ = false;
        memset(instance->gl_sta_ssid, 0, 32);
        memset(instance->gl_sta_bssid_, 0, 6);
        instance->gl_sta_ssid_len_ = 0;
        xEventGroupClearBits(instance->wifi_event_group_, CONNECTED_BIT);
        break;
    case WIFI_EVENT_AP_START:
        esp_wifi_get_mode(&mode);

        /* TODO: get config or information of softap, then set to report extra_info */
        if (instance->ble_is_connected_ == true) {
            if (instance->gl_sta_connected_) {
                esp_blufi_extra_info_t info;
                memset(&info, 0, sizeof(esp_blufi_extra_info_t));
                memcpy(info.sta_bssid, instance->gl_sta_bssid_, 6);
                info.sta_bssid_set = true;
                info.sta_ssid = instance->gl_sta_ssid;
                info.sta_ssid_len = instance->gl_sta_ssid_len_;
                esp_blufi_send_wifi_conn_report(mode, instance->gl_sta_got_ip_ ? ESP_BLUFI_STA_CONN_SUCCESS : ESP_BLUFI_STA_NO_IP, instance->SoftapGetCurrentConnectionNumber(), &info);
            } else if (instance->gl_sta_is_connecting_) {
                esp_blufi_send_wifi_conn_report(mode, ESP_BLUFI_STA_CONNECTING, instance->SoftapGetCurrentConnectionNumber(), &instance->gl_sta_conn_info_);
            } else {
                esp_blufi_send_wifi_conn_report(mode, ESP_BLUFI_STA_CONN_FAIL, instance->SoftapGetCurrentConnectionNumber(), &instance->gl_sta_conn_info_);
            }
        } else {
            BLUFI_INFO("BLUFI BLE is not connected yet\n");
        }
        break;
    case WIFI_EVENT_SCAN_DONE: {
        uint16_t apCount = 0;
        esp_wifi_scan_get_ap_num(&apCount);
        if (apCount == 0) {
            BLUFI_INFO("Nothing AP found");
            break;
        }
        wifi_ap_record_t *ap_list = (wifi_ap_record_t *)malloc(sizeof(wifi_ap_record_t) * apCount);
        if (!ap_list) {
            BLUFI_ERROR("malloc error, ap_list is NULL");
            esp_wifi_clear_ap_list();
            break;
        }
        ESP_ERROR_CHECK(esp_wifi_scan_get_ap_records(&apCount, ap_list));
        esp_blufi_ap_record_t * blufi_ap_list = (esp_blufi_ap_record_t *)malloc(apCount * sizeof(esp_blufi_ap_record_t));
        if (!blufi_ap_list) {
            if (ap_list) {
                free(ap_list);
            }
            BLUFI_ERROR("malloc error, blufi_ap_list is NULL");
            break;
        }
        for (int i = 0; i < apCount; ++i)
        {
            blufi_ap_list[i].rssi = ap_list[i].rssi;
            memcpy(blufi_ap_list[i].ssid, ap_list[i].ssid, sizeof(ap_list[i].ssid));
        }

        if (instance->ble_is_connected_ == true) {
            esp_blufi_send_wifi_list(apCount, blufi_ap_list);
        } else {
            BLUFI_INFO("BLUFI BLE is not connected yet\n");
        }

        esp_wifi_scan_stop();
        free(ap_list);
        free(blufi_ap_list);
        break;
    }
    case WIFI_EVENT_AP_STACONNECTED: {
        wifi_event_ap_staconnected_t* event = (wifi_event_ap_staconnected_t*) event_data;
        BLUFI_INFO("station " MACSTR " join, AID=%d", MAC2STR(event->mac), event->aid);
        break;
    }
    case WIFI_EVENT_AP_STADISCONNECTED: {
        wifi_event_ap_stadisconnected_t* event = (wifi_event_ap_stadisconnected_t*) event_data;
        BLUFI_INFO("station " MACSTR " leave, AID=%d, reason=%d", MAC2STR(event->mac), event->aid, event->reason);
        break;
    }

    default:
        break;
    }
    return;
}

void BluFiManager::HandleBluFiEvent(esp_blufi_cb_event_t event, esp_blufi_cb_param_t* param) {
    switch (event) {
        case ESP_BLUFI_EVENT_INIT_FINISH:
            BLUFI_INFO("BLUFI init finish\n");

            esp_blufi_adv_start();
            UpdateState(State::ADVERTISING, nullptr);
            break;

        case ESP_BLUFI_EVENT_DEINIT_FINISH:
            BLUFI_INFO("BLUFI deinit finish\n");
            break;

        case ESP_BLUFI_EVENT_BLE_CONNECT:
            BLUFI_INFO("BLUFI ble connect\n");
            ble_is_connected_ = true;
            esp_blufi_adv_stop();
            blufi_security_init();
            UpdateState(State::CONNECTED, "BLE connected, ready for WiFi config");
            break;

        case ESP_BLUFI_EVENT_BLE_DISCONNECT:
            BLUFI_INFO("BLUFI ble disconnect\n");
            ble_is_connected_ = false;
            blufi_security_deinit();
            esp_blufi_adv_start();
            UpdateState(State::IDLE, "BLE disconnected");
            break;

        case ESP_BLUFI_EVENT_SET_WIFI_OPMODE:
            BLUFI_INFO("BLUFI Set WIFI opmode %d\n", param->wifi_mode.op_mode);
            ESP_ERROR_CHECK( esp_wifi_set_mode(param->wifi_mode.op_mode) );
            break;

        case ESP_BLUFI_EVENT_REQ_CONNECT_TO_AP:
            BLUFI_INFO("BLUFI request wifi connect to AP\n");
            /* there is no wifi callback when the device has already connected to this wifi
            so disconnect wifi before connection.
            */
            esp_wifi_disconnect();
            BlufiWifiConnect();
            UpdateState(State::CONFIGURING, "Connecting to WiFi...");
            break;

        case ESP_BLUFI_EVENT_REQ_DISCONNECT_FROM_AP:
            BLUFI_INFO("BLUFI request wifi disconnect from AP\n");
            esp_wifi_disconnect();
            UpdateState(State::IDLE, "Disconnecting from WiFi...");
            break;

        case ESP_BLUFI_EVENT_REPORT_ERROR:
            BLUFI_ERROR("BLUFI report error, error code %d\n", param->report_error.state);
            esp_blufi_send_error_info(param->report_error.state);
            if (status_callback_) {
                char msg[64];
                snprintf(msg, sizeof(msg), "BluFi error: %d", param->report_error.state);
                status_callback_(msg);
            }
            break;

        case ESP_BLUFI_EVENT_GET_WIFI_STATUS: {
            wifi_mode_t mode;
            esp_blufi_extra_info_t info;

            esp_wifi_get_mode(&mode);

            if (gl_sta_connected_) {
                memset(&info, 0, sizeof(esp_blufi_extra_info_t));
                memcpy(info.sta_bssid, gl_sta_bssid_, 6);
                info.sta_bssid_set = true;
                info.sta_ssid = gl_sta_ssid;
                info.sta_ssid_len = gl_sta_ssid_len_;
                esp_blufi_send_wifi_conn_report(mode, gl_sta_got_ip_ ? ESP_BLUFI_STA_CONN_SUCCESS : ESP_BLUFI_STA_NO_IP, SoftapGetCurrentConnectionNumber(), &info);
            } else if (gl_sta_is_connecting_) {
                esp_blufi_send_wifi_conn_report(mode, ESP_BLUFI_STA_CONNECTING, SoftapGetCurrentConnectionNumber(), &gl_sta_conn_info_);
            } else {
                esp_blufi_send_wifi_conn_report(mode, ESP_BLUFI_STA_CONN_FAIL, SoftapGetCurrentConnectionNumber(), &gl_sta_conn_info_);
            }
            BLUFI_INFO("BLUFI get wifi status from AP\n");

            break;
        }

        case ESP_BLUFI_EVENT_RECV_SLAVE_DISCONNECT_BLE:
            BLUFI_INFO("blufi close a gatt connection");
            esp_blufi_disconnect();
            break;

        case ESP_BLUFI_EVENT_DEAUTHENTICATE_STA:
            BLUFI_INFO("BluFi deauthenticate STA");
            break;

        case ESP_BLUFI_EVENT_RECV_STA_BSSID:
            memcpy(sta_config_.sta.bssid, param->sta_bssid.bssid, 6);
            sta_config_.sta.bssid_set = 1;
            esp_wifi_set_config(WIFI_IF_STA, &sta_config_);
            BLUFI_INFO("Recv STA BSSID %s\n", sta_config_.sta.ssid);
            break;

        case ESP_BLUFI_EVENT_RECV_STA_SSID:
            if (param->sta_ssid.ssid_len >= sizeof(sta_config_.sta.ssid)/sizeof(sta_config_.sta.ssid[0])) {
                esp_blufi_send_error_info(ESP_BLUFI_DATA_FORMAT_ERROR);
                BLUFI_INFO("Invalid STA SSID\n");
                break;
            }
            strncpy((char *)sta_config_.sta.ssid, (char *)param->sta_ssid.ssid, param->sta_ssid.ssid_len);
            sta_config_.sta.ssid[param->sta_ssid.ssid_len] = '\0';
            esp_wifi_set_config(WIFI_IF_STA, &sta_config_);
            BLUFI_INFO("Recv STA SSID %s\n", sta_config_.sta.ssid);
            UpdateWifiConfig((char *)sta_config_.sta.ssid, param->sta_ssid.ssid_len, nullptr, 0);
            break;

        case ESP_BLUFI_EVENT_RECV_STA_PASSWD:
            if (param->sta_passwd.passwd_len >= sizeof(sta_config_.sta.password)/sizeof(sta_config_.sta.password[0])) {
                esp_blufi_send_error_info(ESP_BLUFI_DATA_FORMAT_ERROR);
                BLUFI_INFO("Invalid STA PASSWORD\n");
                break;
            }
            strncpy((char *)sta_config_.sta.password, (char *)param->sta_passwd.passwd, param->sta_passwd.passwd_len);
            sta_config_.sta.password[param->sta_passwd.passwd_len] = '\0';
            sta_config_.sta.threshold.authmode = BLUFI_WIFI_SCAN_AUTH_MODE_THRESHOLD;
            esp_wifi_set_config(WIFI_IF_STA, &sta_config_);
            BLUFI_INFO("Recv STA PASSWORD %s\n", sta_config_.sta.password);
            UpdateWifiConfig(nullptr, 0, (char *)sta_config_.sta.password, param->sta_passwd.passwd_len);
            break;

        case ESP_BLUFI_EVENT_RECV_SOFTAP_SSID:
            if (param->softap_ssid.ssid_len >= sizeof(ap_config_.ap.ssid)/sizeof(ap_config_.ap.ssid[0])) {
                esp_blufi_send_error_info(ESP_BLUFI_DATA_FORMAT_ERROR);
                BLUFI_INFO("Invalid SOFTAP SSID\n");
                break;
            }
            strncpy((char *)ap_config_.ap.ssid, (char *)param->softap_ssid.ssid, param->softap_ssid.ssid_len);
            ap_config_.ap.ssid[param->softap_ssid.ssid_len] = '\0';
            ap_config_.ap.ssid_len = param->softap_ssid.ssid_len;
            esp_wifi_set_config(WIFI_IF_AP, &ap_config_);
            BLUFI_INFO("Recv SOFTAP SSID %s, ssid len %d\n", ap_config_.ap.ssid, ap_config_.ap.ssid_len);
            break;

        case ESP_BLUFI_EVENT_RECV_SOFTAP_PASSWD:
            if (param->softap_passwd.passwd_len >= sizeof(ap_config_.ap.password)/sizeof(ap_config_.ap.password[0])) {
                esp_blufi_send_error_info(ESP_BLUFI_DATA_FORMAT_ERROR);
                BLUFI_INFO("Invalid SOFTAP PASSWD\n");
                break;
            }
            strncpy((char *)ap_config_.ap.password, (char *)param->softap_passwd.passwd, param->softap_passwd.passwd_len);
            ap_config_.ap.password[param->softap_passwd.passwd_len] = '\0';
            esp_wifi_set_config(WIFI_IF_AP, &ap_config_);
            BLUFI_INFO("Recv SOFTAP PASSWORD %s len = %d\n", ap_config_.ap.password, param->softap_passwd.passwd_len);
            break;

        case ESP_BLUFI_EVENT_RECV_SOFTAP_MAX_CONN_NUM:
            if (param->softap_max_conn_num.max_conn_num > 4) {
                return;
            }
            ap_config_.ap.max_connection = param->softap_max_conn_num.max_conn_num;
            esp_wifi_set_config(WIFI_IF_AP, &ap_config_);
            BLUFI_INFO("Recv SOFTAP MAX CONN NUM %d\n", ap_config_.ap.max_connection);
            break;

        case ESP_BLUFI_EVENT_RECV_SOFTAP_AUTH_MODE:
            if (param->softap_auth_mode.auth_mode >= WIFI_AUTH_MAX) {
                return;
            }
            ap_config_.ap.authmode = param->softap_auth_mode.auth_mode;
            esp_wifi_set_config(WIFI_IF_AP, &ap_config_);
            BLUFI_INFO("Recv SOFTAP AUTH MODE %d\n", ap_config_.ap.authmode);
            break;
        case ESP_BLUFI_EVENT_RECV_SOFTAP_CHANNEL:
            if (param->softap_channel.channel > 13) {
                return;
            }
            ap_config_.ap.channel = param->softap_channel.channel;
            esp_wifi_set_config(WIFI_IF_AP, &ap_config_);
            BLUFI_INFO("Recv SOFTAP CHANNEL %d\n", ap_config_.ap.channel);
            break;
        case ESP_BLUFI_EVENT_GET_WIFI_LIST:{
            wifi_scan_config_t scanConf = {
                .ssid = NULL,
                .bssid = NULL,
                .channel = 0,
                .show_hidden = false
            };
            esp_err_t ret = esp_wifi_scan_start(&scanConf, true);
            if (ret != ESP_OK) {
                esp_blufi_send_error_info(ESP_BLUFI_WIFI_SCAN_FAIL);
            }
            break;
        }
        case ESP_BLUFI_EVENT_RECV_CUSTOM_DATA:
            BLUFI_INFO("Recv Custom Data %" PRIu32 "\n", param->custom_data.data_len);
            ESP_LOG_BUFFER_HEX("Custom Data", param->custom_data.data, param->custom_data.data_len);
            break;

        case ESP_BLUFI_EVENT_RECV_USERNAME:
            BLUFI_INFO("BluFi recv username");
            break;

        case ESP_BLUFI_EVENT_RECV_CA_CERT:
            BLUFI_INFO("BluFi recv CA cert");
            break;

        case ESP_BLUFI_EVENT_RECV_CLIENT_CERT:
            BLUFI_INFO("BluFi recv client cert");
            break;

        case ESP_BLUFI_EVENT_RECV_SERVER_CERT:
            BLUFI_INFO("BluFi recv server cert");
            break;

        case ESP_BLUFI_EVENT_RECV_CLIENT_PRIV_KEY:
            BLUFI_INFO("BluFi recv client private key");
            break;

        case ESP_BLUFI_EVENT_RECV_SERVER_PRIV_KEY:
            BLUFI_INFO("BluFi recv server private key");
            break;

        default:
            BLUFI_INFO("BluFi event: %d", event);
            break;
    }
}

// 静态回调函数
void BluFiManager::BluFiEventCallback(esp_blufi_cb_event_t event, esp_blufi_cb_param_t* param) {
    if (instance_) {
        instance_->HandleBluFiEvent(event, param);
    }
}

void BluFiManager::InitialiseWifi(void)
{
    ESP_ERROR_CHECK(esp_netif_init());
    wifi_event_group_ = xEventGroupCreate();
    // app-main已经执行
    // ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_t *sta_netif = esp_netif_create_default_wifi_sta();
    assert(sta_netif);
    esp_netif_t *ap_netif = esp_netif_create_default_wifi_ap();
    assert(ap_netif);
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &WifiEventHandler, this));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &IpEventHandler, this));

    // 重要: 否则wifi连接失败
    // wifi:coexist: wi-fi connect fail, apply reconnect coex policy
    sta_config_.ap.authmode = WIFI_AUTH_OPEN;

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK( esp_wifi_init(&cfg) );
    ESP_ERROR_CHECK( esp_wifi_set_mode(WIFI_MODE_STA) );
    BlufiRecordWifiConnInfo(BLUFI_INVALID_RSSI, BLUFI_INVALID_REASON);
    ESP_ERROR_CHECK( esp_wifi_start() );
}

void BluFiManager::BlufiRecordWifiConnInfo(int rssi, uint8_t reason)
{
    memset(&gl_sta_conn_info_, 0, sizeof(esp_blufi_extra_info_t));
    if (gl_sta_is_connecting_) {
        gl_sta_conn_info_.sta_max_conn_retry_set = true;
        gl_sta_conn_info_.sta_max_conn_retry = BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY;
    } else {
        gl_sta_conn_info_.sta_conn_rssi_set = true;
        gl_sta_conn_info_.sta_conn_rssi = rssi;
        gl_sta_conn_info_.sta_conn_end_reason_set = true;
        gl_sta_conn_info_.sta_conn_end_reason = reason;
    }
}

void BluFiManager::BlufiWifiConnect(void)
{
    blufi_wifi_retry_ = 0;
    if (!wifi_config_.is_configured) {
        BLUFI_ERROR("BLUFI WiFi try connect, but not configured ...");
        return;
    }
    BLUFI_INFO("BLUFI WiFi starts connect ...");
    gl_sta_is_connecting_ = (esp_wifi_connect() == ESP_OK);
    BlufiRecordWifiConnInfo(BLUFI_INVALID_RSSI, BLUFI_INVALID_REASON);
}

bool BluFiManager::BlufiWifiReconnect(void)
{
    bool ret = false;
    if (wifi_config_.is_configured && gl_sta_is_connecting_ && blufi_wifi_retry_++ < BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY) {
        BLUFI_INFO("BLUFI WiFi starts reconnection\n");
        gl_sta_is_connecting_ = (esp_wifi_connect() == ESP_OK);
        BlufiRecordWifiConnInfo(BLUFI_INVALID_RSSI, BLUFI_INVALID_REASON);
        ret = true;
    }
    return ret;
}


const char* GetBluFiStateName(BluFiManager::State state) {
    switch (state) {
        case BluFiManager::State::DISABLED: return "DISABLED";
        case BluFiManager::State::IDLE: return "IDLE";
        case BluFiManager::State::ADVERTISING: return "ADVERTISING";
        case BluFiManager::State::CONNECTED: return "CONNECTED";
        case BluFiManager::State::CONFIGURING: return "CONFIGURING";
        case BluFiManager::State::CONNECTED_TO_WIFI: return "CONNECTED_TO_WIFI";
        default: return "UNKNOWN";
    }
}
