# ESP-ADC PMIC实现指南

## 🔋 概述

本文档介绍了基于ESP-ADC的PMIC（电源管理集成电路）实现，提供完整的电池管理和电源监控功能。

## ✨ 功能特性

### 核心功能
- **电池电压监测**: 高精度ADC电压测量
- **电池电量计算**: 基于电压的电量百分比计算
- **充电状态检测**: GPIO引脚检测充电状态
- **USB电源检测**: USB连接和供电状态检测
- **电源管理**: 低功耗模式和电源控制
- **健康状态监测**: 电池健康度评估
- **统计信息**: 详细的电源使用统计

### 硬件支持
- **ADC单元**: 支持ADC_UNIT_1和ADC_UNIT_2
- **校准功能**: 自动ADC校准提高精度
- **多种衰减**: 支持不同的ADC衰减配置
- **GPIO检测**: 充电状态GPIO检测

## 🔧 硬件配置

### 典型配置示例

#### Waveshare S3 Touch AMOLED
```cpp
#define BSP_BAT_ADC_CHAN  (ADC_CHANNEL_6)    // GPIO17
#define BSP_BAT_ADC_ATTEN (ADC_ATTEN_DB_2_5) // 0 ~ 1100 mV
#define BSP_BAT_VOL_RATIO ((62 + 20) / 20)   // 分压比 4.1
#define CHARGING_STATUS_PIN GPIO_NUM_18
```

#### SenseCap Watcher
```cpp
#define BSP_BAT_ADC_CHAN  (ADC_CHANNEL_2)    // GPIO3
#define BSP_BAT_ADC_ATTEN (ADC_ATTEN_DB_2_5) // 0 ~ 1100 mV
#define BSP_BAT_VOL_RATIO ((62 + 20) / 20)   // 分压比 4.1
```

#### ESP Spot S3
```cpp
#define VBAT_ADC_CHANNEL         ADC_CHANNEL_9  // GPIO10
#define ADC_ATTEN                ADC_ATTEN_DB_12
#define FULL_BATTERY_VOLTAGE     4100
#define EMPTY_BATTERY_VOLTAGE    3200
```

## 📋 API接口

### 基础电池功能
```cpp
class Pmic {
public:
    // 构造函数
    Pmic(int charging_pin);
    
    // 基础电池信息
    uint16_t BatterygetVoltage(void);           // 获取电池电压(mV)
    uint8_t BatterygetPercent(bool print);      // 获取电池电量(%)
    float GetBatteryVoltage();                  // 获取电池电压(V)
    int GetBatteryLevel();                      // 获取电池电量等级
    
    // 充电状态
    bool IsCharging();                          // 是否正在充电
    bool IsDischarging();                       // 是否正在放电
    bool IsChargingDone();                      // 充电是否完成
    
    // 电源检测
    bool IsUsbConnected();                      // USB是否连接
    bool IsUsbPowered();                        // 是否USB供电
    bool IsBatteryPresent();                    // 电池是否存在
    int GetPowerSource();                       // 获取电源来源
    
    // 系统控制
    void PowerOff();                            // 系统关机
    float GetSystemVoltage();                   // 获取系统电压
    float GetTemperature();                     // 获取温度
};
```

### 高级功能
```cpp
// 电池健康状态
bool IsBatteryHealthy();                        // 电池是否健康
float GetBatteryHealth();                       // 获取健康度(0.0-1.0)

// 电源管理
void EnableLowPowerMode(bool enable);           // 启用低功耗模式
bool IsLowPowerMode();                          // 是否低功耗模式
void SetBatteryThresholds(float low, float critical); // 设置电池阈值

// 统计信息
struct PowerStats {
    uint32_t total_charge_cycles;               // 总充电循环次数
    uint32_t total_runtime_minutes;             // 总运行时间(分钟)
    float avg_battery_voltage;                  // 平均电池电压
    float min_battery_voltage;                  // 最低电池电压
    float max_battery_voltage;                  // 最高电池电压
    uint32_t low_battery_events;                // 低电量事件次数
    uint32_t charging_events;                   // 充电事件次数
};

PowerStats GetPowerStats();                     // 获取统计信息
void ResetPowerStats();                         // 重置统计信息
```

## 🚀 使用示例

### 基础使用
```cpp
#include "pmic.h"

// 创建PMIC实例
Pmic pmic(CHARGING_STATUS_PIN);

void setup() {
    // 获取电池信息
    uint16_t voltage_mv = pmic.BatterygetVoltage();
    uint8_t percentage = pmic.BatterygetPercent(true);
    
    ESP_LOGI("PMIC", "Battery: %dmV, %d%%", voltage_mv, percentage);
    
    // 检查充电状态
    if (pmic.IsCharging()) {
        ESP_LOGI("PMIC", "Battery is charging");
    } else if (pmic.IsDischarging()) {
        ESP_LOGI("PMIC", "Battery is discharging");
    }
    
    // 检查电源来源
    int power_source = pmic.GetPowerSource();
    switch (power_source) {
        case 0: ESP_LOGI("PMIC", "Running on battery"); break;
        case 1: ESP_LOGI("PMIC", "Running on USB power"); break;
        case 2: ESP_LOGI("PMIC", "USB connected with battery"); break;
        default: ESP_LOGW("PMIC", "No power source detected"); break;
    }
}
```

### 高级功能使用
```cpp
void advanced_power_management() {
    Pmic pmic(CHARGING_STATUS_PIN);
    
    // 设置电池阈值
    pmic.SetBatteryThresholds(3.4f, 3.2f);  // 低电量3.4V，临界3.2V
    
    // 检查电池健康状态
    if (pmic.IsBatteryHealthy()) {
        float health = pmic.GetBatteryHealth();
        ESP_LOGI("PMIC", "Battery health: %.1f%%", health * 100);
    } else {
        ESP_LOGW("PMIC", "Battery health issues detected!");
    }
    
    // 启用低功耗模式
    if (pmic.GetBatteryLevel() < 20) {
        pmic.EnableLowPowerMode(true);
        ESP_LOGI("PMIC", "Low power mode enabled");
    }
    
    // 获取详细统计信息
    auto stats = pmic.GetPowerStats();
    ESP_LOGI("PMIC", "Power Statistics:");
    ESP_LOGI("PMIC", "- Charge cycles: %lu", stats.total_charge_cycles);
    ESP_LOGI("PMIC", "- Runtime: %lu minutes", stats.total_runtime_minutes);
    ESP_LOGI("PMIC", "- Avg voltage: %.2fV", stats.avg_battery_voltage);
    ESP_LOGI("PMIC", "- Voltage range: %.2fV - %.2fV", 
             stats.min_battery_voltage, stats.max_battery_voltage);
    ESP_LOGI("PMIC", "- Low battery events: %lu", stats.low_battery_events);
    ESP_LOGI("PMIC", "- Charging events: %lu", stats.charging_events);
}
```

### 电池监控任务
```cpp
void battery_monitor_task(void* param) {
    Pmic* pmic = (Pmic*)param;
    
    while (1) {
        // 定期检查电池状态
        uint8_t battery_level = pmic->BatterygetPercent();
        float battery_voltage = pmic->GetBatteryVoltage();
        
        ESP_LOGI("Battery", "Level: %d%%, Voltage: %.2fV", battery_level, battery_voltage);
        
        // 低电量警告
        if (battery_level < 20 && !pmic->IsCharging()) {
            ESP_LOGW("Battery", "Low battery warning!");
            
            if (battery_level < 10) {
                ESP_LOGE("Battery", "Critical battery level! Entering power save mode");
                pmic->EnableLowPowerMode(true);
            }
        }
        
        // 充电状态变化检测
        static bool last_charging = false;
        bool current_charging = pmic->IsCharging();
        
        if (current_charging != last_charging) {
            if (current_charging) {
                ESP_LOGI("Battery", "Charging started");
            } else {
                ESP_LOGI("Battery", "Charging stopped");
                if (pmic->IsChargingDone()) {
                    ESP_LOGI("Battery", "Charging completed");
                }
            }
            last_charging = current_charging;
        }
        
        vTaskDelay(pdMS_TO_TICKS(5000));  // 每5秒检查一次
    }
}

// 启动监控任务
void start_battery_monitoring() {
    static Pmic pmic(CHARGING_STATUS_PIN);
    
    xTaskCreate(battery_monitor_task, "battery_monitor", 4096, &pmic, 5, NULL);
}
```

## ⚙️ 配置参数

### ADC配置
```cpp
// ADC单元选择
#define ADC_UNIT ADC_UNIT_2

// ADC通道配置
#define BSP_BAT_ADC_CHAN ADC_CHANNEL_6

// ADC衰减配置
#define BSP_BAT_ADC_ATTEN ADC_ATTEN_DB_2_5  // 0-1100mV
// 或者
#define BSP_BAT_ADC_ATTEN ADC_ATTEN_DB_12   // 0-3300mV

// 电压分压比
#define BSP_BAT_VOL_RATIO 4.1  // (R1+R2)/R2
```

### 电池参数
```cpp
// 电池电压范围
static constexpr float BATTERY_MIN_VOLTAGE = 3.2f;   // 最低电压
static constexpr float BATTERY_MAX_VOLTAGE = 4.2f;   // 最高电压
static constexpr float BATTERY_NOMINAL_VOLTAGE = 3.7f; // 标称电压

// 电池阈值
float low_battery_threshold = 3.4f;      // 低电量阈值
float critical_battery_threshold = 3.2f; // 临界电量阈值
```

## 🔍 故障排除

### 常见问题

#### 1. ADC读取失败
```cpp
// 检查ADC配置
ESP_LOGI("PMIC", "ADC Unit: %d, Channel: %d, Atten: %d", 
         ADC_UNIT_2, BSP_BAT_ADC_CHAN, BSP_BAT_ADC_ATTEN);

// 检查GPIO配置
ESP_LOGI("PMIC", "Battery ADC GPIO: %d", ADC_CHANNEL_TO_GPIO(BSP_BAT_ADC_CHAN));
```

#### 2. 电压读数不准确
```cpp
// 检查分压比配置
ESP_LOGI("PMIC", "Voltage ratio: %.2f", (float)BSP_BAT_VOL_RATIO);

// 校准ADC
if (cali_handle_) {
    ESP_LOGI("PMIC", "ADC calibration enabled");
} else {
    ESP_LOGW("PMIC", "ADC calibration failed, using raw values");
}
```

#### 3. 充电检测不工作
```cpp
// 检查充电检测引脚
ESP_LOGI("PMIC", "Charging pin: %d, state: %d", 
         charging_pin_, gpio_get_level((gpio_num_t)charging_pin_));

// 检查引脚配置
gpio_config_t io_conf = {};
io_conf.pin_bit_mask = (1ULL << charging_pin_);
io_conf.mode = GPIO_MODE_INPUT;
io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
gpio_config(&io_conf);
```

## 📊 性能优化

### 低功耗优化
```cpp
// 在低功耗模式下减少ADC采样频率
if (pmic.IsLowPowerMode()) {
    vTaskDelay(pdMS_TO_TICKS(30000));  // 30秒采样一次
} else {
    vTaskDelay(pdMS_TO_TICKS(5000));   // 5秒采样一次
}
```

### 精度优化
```cpp
// 多次采样取平均值
uint32_t voltage_sum = 0;
const int samples = 10;

for (int i = 0; i < samples; i++) {
    voltage_sum += pmic.BatterygetVoltage();
    vTaskDelay(pdMS_TO_TICKS(10));
}

uint16_t avg_voltage = voltage_sum / samples;
```

## 总结

基于ESP-ADC的PMIC实现提供了：

1. **✅ 完整的电池管理功能** - 电压、电量、充电状态监测
2. **✅ 高精度ADC测量** - 自动校准和多种衰减支持
3. **✅ 智能电源管理** - 低功耗模式和阈值管理
4. **✅ 详细的统计信息** - 充电循环、运行时间等统计
5. **✅ 健康状态监测** - 电池健康度评估
6. **✅ 易于集成** - 简单的API接口和配置

这个实现为ESP32设备提供了专业级的电源管理能力！🔋⚡
