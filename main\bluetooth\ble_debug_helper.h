#pragma once

#include <esp_log.h>

#ifdef CONFIG_BT_ENABLED
#include <esp_bt.h>
#include <esp_gap_ble_api.h>
#endif

/**
 * @brief BLE调试助手类
 */
class BleDebugHelper {
public:
    /**
     * @brief 显示BLE设备发现指南
     */
    static void ShowDiscoveryGuide() {
        ESP_LOGI("BleDebugHelper", "=== BLE设备发现指南 ===");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "📱 手机端操作:");
        ESP_LOGI("BleDebugHelper", "1. 打开蓝牙设置");
        ESP_LOGI("BleDebugHelper", "2. 搜索新设备");
        ESP_LOGI("BleDebugHelper", "3. 查找 'XiaoZhi-ESP32'");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "💻 电脑端操作:");
        ESP_LOGI("BleDebugHelper", "Windows:");
        ESP_LOGI("BleDebugHelper", "  1. 设置 -> 蓝牙和其他设备");
        ESP_LOGI("BleDebugHelper", "  2. 添加蓝牙或其他设备");
        ESP_LOGI("BleDebugHelper", "  3. 选择蓝牙");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "macOS:");
        ESP_LOGI("BleDebugHelper", "  1. 系统偏好设置 -> 蓝牙");
        ESP_LOGI("BleDebugHelper", "  2. 等待设备出现");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "Linux:");
        ESP_LOGI("BleDebugHelper", "  1. bluetoothctl");
        ESP_LOGI("BleDebugHelper", "  2. scan on");
        ESP_LOGI("BleDebugHelper", "  3. devices");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "🔧 调试工具:");
        ESP_LOGI("BleDebugHelper", "- nRF Connect (手机App)");
        ESP_LOGI("BleDebugHelper", "- LightBlue (iOS/macOS)");
        ESP_LOGI("BleDebugHelper", "- BLE Scanner (Android)");
        ESP_LOGI("BleDebugHelper", "");
    }

    /**
     * @brief 显示常见问题解决方案
     */
    static void ShowTroubleshooting() {
        ESP_LOGI("BleDebugHelper", "=== BLE设备发现问题排查 ===");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "❌ 设备无法被发现的可能原因:");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "1. 广播数据问题:");
        ESP_LOGI("BleDebugHelper", "   - 缺少设备名称");
        ESP_LOGI("BleDebugHelper", "   - 广播标志不正确");
        ESP_LOGI("BleDebugHelper", "   - 广播间隔过长");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "2. 地址类型问题:");
        ESP_LOGI("BleDebugHelper", "   - 使用随机地址但未正确设置");
        ESP_LOGI("BleDebugHelper", "   - 地址类型与广播参数不匹配");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "3. 功率和信号问题:");
        ESP_LOGI("BleDebugHelper", "   - 发射功率过低");
        ESP_LOGI("BleDebugHelper", "   - 天线问题");
        ESP_LOGI("BleDebugHelper", "   - 距离过远");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "4. 系统配置问题:");
        ESP_LOGI("BleDebugHelper", "   - BLE功能未完全启用");
        ESP_LOGI("BleDebugHelper", "   - 兼容性配置错误");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "✅ 解决步骤:");
        ESP_LOGI("BleDebugHelper", "1. 检查广播是否真正启动");
        ESP_LOGI("BleDebugHelper", "2. 验证广播数据设置");
        ESP_LOGI("BleDebugHelper", "3. 使用专业BLE扫描工具");
        ESP_LOGI("BleDebugHelper", "4. 检查设备距离和环境");
        ESP_LOGI("BleDebugHelper", "5. 重启蓝牙服务");
        ESP_LOGI("BleDebugHelper", "");
    }

    /**
     * @brief 显示BLE测试命令
     */
    static void ShowTestCommands() {
        ESP_LOGI("BleDebugHelper", "=== BLE测试命令 ===");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "🐧 Linux测试命令:");
        ESP_LOGI("BleDebugHelper", "sudo hcitool lescan");
        ESP_LOGI("BleDebugHelper", "sudo bluetoothctl");
        ESP_LOGI("BleDebugHelper", "> scan on");
        ESP_LOGI("BleDebugHelper", "> devices");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "🪟 Windows PowerShell:");
        ESP_LOGI("BleDebugHelper", "Get-PnpDevice | Where-Object {$_.Class -eq 'Bluetooth'}");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "🍎 macOS终端:");
        ESP_LOGI("BleDebugHelper", "system_profiler SPBluetoothDataType");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "📱 手机App推荐:");
        ESP_LOGI("BleDebugHelper", "- nRF Connect for Mobile (Nordic)");
        ESP_LOGI("BleDebugHelper", "- BLE Scanner (Bluepixel)");
        ESP_LOGI("BleDebugHelper", "- LightBlue Explorer (Punch Through)");
        ESP_LOGI("BleDebugHelper", "");
    }

    /**
     * @brief 显示广播数据分析
     */
    static void ShowAdvertisingDataAnalysis() {
        ESP_LOGI("BleDebugHelper", "=== 广播数据分析 ===");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "📡 当前广播配置:");
        ESP_LOGI("BleDebugHelper", "设备名称: XiaoZhi-ESP32");
        ESP_LOGI("BleDebugHelper", "广播类型: ADV_TYPE_IND (可连接不定向)");
        ESP_LOGI("BleDebugHelper", "地址类型: BLE_ADDR_TYPE_PUBLIC");
        ESP_LOGI("BleDebugHelper", "广播间隔: 20-40ms");
        ESP_LOGI("BleDebugHelper", "广播信道: 所有信道 (37, 38, 39)");
        ESP_LOGI("BleDebugHelper", "过滤策略: 允许所有扫描和连接");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "🏷️  广播标志:");
        ESP_LOGI("BleDebugHelper", "- General Discoverable (可被一般发现)");
        ESP_LOGI("BleDebugHelper", "- BR/EDR Not Supported (不支持经典蓝牙)");
        ESP_LOGI("BleDebugHelper", "");
        ESP_LOGI("BleDebugHelper", "📊 包含的数据:");
        ESP_LOGI("BleDebugHelper", "- 设备名称 (完整)");
        ESP_LOGI("BleDebugHelper", "- 发射功率");
        ESP_LOGI("BleDebugHelper", "- 制造商数据 ('XiaoZhi')");
        ESP_LOGI("BleDebugHelper", "- 连接间隔范围");
        ESP_LOGI("BleDebugHelper", "");
    }

};
