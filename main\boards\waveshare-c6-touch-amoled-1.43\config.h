#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>


#define AUDIO_INPUT_SAMPLE_RATE 24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_INPUT_REFERENCE    false

#define AUDIO_I2S_GPIO_MCLK  GPIO_NUM_19
#define AUDIO_I2S_GPIO_WS    GPIO_NUM_22
#define AUDIO_I2S_GPIO_BCLK  GPIO_NUM_21
#define AUDIO_I2S_GPIO_DIN   GPIO_NUM_20
#define AUDIO_I2S_GPIO_DOUT  GPIO_NUM_23

#define AUDIO_CODEC_PA_PIN       GPIO_NUM_NC
#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_18
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_8
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR  ES7210_CODEC_DEFAULT_ADDR

#define I2C_Touch_ADDRESS   0x38
#define I2C_ADDRESS         ESP_IO_EXPANDER_I2C_TCA9554_ADDRESS_000
#define BOOT_BUTTON_GPIO        GPIO_NUM_9
#define PWR_BUTTON_GPIO         GPIO_NUM_2

#define LCD_CS       GPIO_NUM_10
#define LCD_PCLK     GPIO_NUM_11
#define LCD_D0       GPIO_NUM_4 
#define LCD_D1       GPIO_NUM_5
#define LCD_D2       GPIO_NUM_6
#define LCD_D3       GPIO_NUM_7
#define LCD_RST      GPIO_NUM_3
#define LCD_LIGHT    (-1)

#define EXAMPLE_LCD_H_RES 466
#define EXAMPLE_LCD_V_RES 466
 
#define DISPLAY_MIRROR_X false
#define DISPLAY_MIRROR_Y false
#define DISPLAY_SWAP_XY false

#define DISPLAY_OFFSET_X  0
#define DISPLAY_OFFSET_Y  0


#endif // _BOARD_CONFIG_H_
