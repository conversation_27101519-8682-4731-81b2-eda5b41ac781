#include "pwr_manager.h"

static const char* TAG = "PwrBtnManagement";

PwrBtnManagement::PwrBtnManagement(Pmic* pmic
        , uint32_t monitor_period
)
        : pmic_(pmic)
        , monitor_period_(monitor_period)
{
    esp_timer_create_args_t timer_args = {
        .callback = [](void* arg) {
            auto self = static_cast<PwrBtnManagement*>(arg);
            self->OnPwrDetectTimer();
        },
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "pwrkey_monitor_timer",
        .skip_unhandled_events = true,
    };
    ESP_ERROR_CHECK(esp_timer_create(&timer_args, &monitor_timer_));
}

PwrBtnManagement::~PwrBtnManagement(){
    if (monitor_timer_) {
        ESP_ERROR_CHECK(esp_timer_stop(monitor_timer_));
        ESP_ERROR_CHECK(esp_timer_delete(monitor_timer_));
        monitor_timer_ = nullptr;
    }
}

void PwrBtnManagement::OnPwrDetectTimer() {
    // Get PMU Interrupt Status Register
    //pmic_->getIrqStatus();

    bool pwr_short_pressed = pmic_->isPekeyShortPressIrq();
    bool pwr_long_pressed = pmic_->isPekeyLongPressIrq();
    pwr_is_pressed_ = pwr_short_pressed || pwr_long_pressed;

    // Clear PMU Interrupt Status Register
    pmic_->clearIrqStatus();

    if (pwr_short_pressed) {
        if (on_short_press_) {
            on_short_press_();
        }
        return;
    }
    if (pwr_long_pressed) {
        if (on_long_press_) {
            on_long_press_();
        }
        return;
    }
}

void PwrBtnManagement::SetEnabled(bool enabled) {
    if (enabled && !enabled_) {
        enabled_ = enabled;
        ESP_ERROR_CHECK(esp_timer_start_periodic(monitor_timer_, monitor_period_));
        ESP_LOGI(TAG, "PowerKey detect timer enabled");
    } else if (!enabled && enabled_) {
        ESP_ERROR_CHECK(esp_timer_stop(monitor_timer_));
        enabled_ = enabled;
        ESP_LOGI(TAG, "PowerKey detect timer disabled");
    }
}

void PwrBtnManagement::OnShortPress(std::function<void()> callback) {
    on_short_press_ = callback;
}

void PwrBtnManagement::OnLongPress(std::function<void()> callback) {
    on_long_press_ = callback;
}
