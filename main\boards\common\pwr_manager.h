#pragma once

#include <esp_log.h>
#include <functional>
#include <esp_timer.h>
#include "pmic.h"

/**
 * @brief 电源按键管理类
 */
class PwrBtnManagement {
private:
    Pmic* pmic_;
    /**
     * @brief 电源按键监控定时器周期，默认50s
     */
    uint32_t monitor_period_;
    /**
     * @brief 电源按键监控定时器
     */
    esp_timer_handle_t monitor_timer_;
    /**
     * @brief 是否正在按键
     */
    bool pwr_is_pressed_ = false;
    /**
     * @brief 是否已启用
     */
    bool enabled_ = false;

    /**
     * @brief 电源按键定时器回调函数
     */
    void OnPwrDetectTimer();

    /**
     * @brief 电源按键短按的回调函数
     */
    std::function<void()> on_short_press_;
    /**
     * @brief 电源按键长按的回调函数
     */
    std::function<void()> on_long_press_;

public:
    PwrBtnManagement(Pmic* pmic
        , uint32_t monitor_period
    );
    ~PwrBtnManagement();

    /**
     * @brief 启用电源按键管理
     * @param enabled 是否启用: true=启用
     */
    void SetEnabled(bool enabled);

    /**
     * @brief 设置电源按键短按的回调函数
     */
    void OnShortPress(std::function<void()> callback);

    /**
     * @brief 设置电源按键长按的回调函数
     */
    void OnLongPress(std::function<void()> callback);
};
