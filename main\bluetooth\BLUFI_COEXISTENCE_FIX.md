# BluFi Wi-Fi和蓝牙共存问题修复

## 🚨 问题描述

在使用BluFi功能时出现以下错误：
```
wifi:Coexist: Wi-Fi connect fail, apply reconnect coex policy
```

这个错误表明Wi-Fi和蓝牙在同时工作时出现了共存冲突，导致Wi-Fi连接失败。

## 🔍 问题分析

### 根本原因
1. **频段冲突**: Wi-Fi和蓝牙都工作在2.4GHz频段
2. **共存策略不当**: 默认的共存策略可能不适合BluFi场景
3. **功率管理问题**: Wi-Fi功率设置过高可能干扰蓝牙
4. **重连策略缺陷**: 连接失败后的重连策略没有考虑共存问题

### ESP32-S3共存机制
ESP32-S3内置硬件共存机制，但需要正确配置：
- **时分复用**: Wi-Fi和蓝牙轮流使用射频
- **优先级管理**: 可以设置Wi-Fi或蓝牙的优先级
- **功率控制**: 降低发射功率减少相互干扰

## ✅ 解决方案

### 1. 共存策略配置

**新增方法**: `ConfigureCoexistence()`
```cpp
void BluFiManager::ConfigureCoexistence() {
    // 设置平衡的共存策略
    if (SetCoexPreference(ESP_COEX_PREFER_BALANCE)) {
        ESP_LOGI(TAG, "✅ Coex preference set to BALANCE");
    }
}
```

**辅助方法**: `SetCoexPreference()`
```cpp
bool BluFiManager::SetCoexPreference(esp_coex_preference_t preference) {
    esp_err_t ret = esp_coex_preference_set(preference);
    if (ret == ESP_OK) {
        current_coex_preference_ = preference; // 跟踪当前状态
        return true;
    }
    return false;
}
```

### 2. Wi-Fi配置优化

**改进的Wi-Fi初始化**:
```cpp
wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
// 禁用AMPDU以改善共存
cfg.ampdu_rx_enable = 0;
cfg.ampdu_tx_enable = 0;

// 降低发射功率减少干扰
esp_wifi_set_max_tx_power(44); // 11dBm

// 启用省电模式
esp_wifi_set_ps(WIFI_PS_MIN_MODEM);
```

### 3. 智能重连策略

**改进的重连逻辑**:
```cpp
bool BluFiManager::BlufiWifiReconnect(void) {
    if (blufi_wifi_retry_++ < CONFIG_BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY) {
        // 重连前等待，让蓝牙有机会工作
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 重新应用共存策略
        esp_coex_preference_set(ESP_COEX_PREFER_BALANCE);
        
        return esp_wifi_connect() == ESP_OK;
    } else {
        // 最后尝试：临时提高Wi-Fi优先级
        esp_coex_preference_set(ESP_COEX_PREFER_WIFI);
        vTaskDelay(pdMS_TO_TICKS(2000));
        esp_coex_preference_set(ESP_COEX_PREFER_BALANCE);
    }
    return false;
}
```

### 4. 断开连接处理

**智能断开检测**:
```cpp
case WIFI_EVENT_STA_DISCONNECTED:
    // 检查是否是共存相关的断开
    if (disconnected_event->reason == WIFI_REASON_UNSPECIFIED ||
        disconnected_event->reason == WIFI_REASON_AUTH_EXPIRE) {
        
        // 应用共存恢复策略
        esp_coex_preference_set(ESP_COEX_PREFER_WIFI);
        vTaskDelay(pdMS_TO_TICKS(500));
        esp_coex_preference_set(ESP_COEX_PREFER_BALANCE);
    }
```

### 5. 诊断功能

**新增诊断方法**: `DiagnoseCoexistenceIssues()`
```cpp
void BluFiManager::DiagnoseCoexistenceIssues() {
    // 检查共存偏好设置
    esp_coex_preference_t pref = esp_coex_preference_get();
    
    // 检查Wi-Fi功率设置
    int8_t power;
    esp_wifi_get_max_tx_power(&power);
    
    // 检查省电模式
    wifi_ps_type_t ps_type;
    esp_wifi_get_ps(&ps_type);
    
    // 输出诊断信息
    ESP_LOGI(TAG, "Coex preference: %d, WiFi power: %.2f dBm, PS mode: %d", 
             pref, power * 0.25, ps_type);
}
```

## 🔧 配置参数

### sdkconfig中的关键配置
```ini
# 启用共存支持
CONFIG_ESP_COEX_ENABLED=y
CONFIG_ESP_COEX_SW_COEXIST_ENABLE=y

# Wi-Fi共存配置
CONFIG_ESP_WIFI_SW_COEXIST_ENABLE=y

# BluFi重连次数
CONFIG_BLUFI_WIFI_CONNECTION_MAXIMUM_RETRY=2

# Wi-Fi功率限制
CONFIG_ESP_PHY_MAX_WIFI_TX_POWER=20
```

## 📊 性能优化

### 共存策略对比

| 策略 | Wi-Fi性能 | 蓝牙性能 | 推荐场景 |
|------|-----------|----------|----------|
| `ESP_COEX_PREFER_WIFI` | 高 | 低 | 大数据传输 |
| `ESP_COEX_PREFER_BT` | 低 | 高 | 音频流传输 |
| `ESP_COEX_PREFER_BALANCE` | 中 | 中 | BluFi配置 ⭐ |

### 功率设置建议

| 场景 | 发射功率 | 说明 |
|------|----------|------|
| 近距离配置 | 8-11 dBm | 减少干扰 ⭐ |
| 远距离连接 | 15-20 dBm | 提高覆盖 |
| 密集环境 | 5-8 dBm | 最小干扰 |

## 🧪 测试验证

### 1. 功能测试
```cpp
// 在BluFi初始化后调用
blufi_manager->DiagnoseCoexistenceIssues();
```

### 2. 连接测试
- 启动BluFi配置
- 连接手机蓝牙
- 配置Wi-Fi网络
- 观察连接成功率

### 3. 稳定性测试
- 多次连接/断开Wi-Fi
- 检查是否出现共存错误
- 验证重连机制

## 🚀 使用指南

### 1. 启用修复
修复已自动集成到BluFi管理器中，无需额外配置。

### 2. 监控日志
关注以下日志信息：
```
I (xxx) BluFiManager: ✅ Coexistence configured successfully
I (xxx) BluFiManager: Coex preference set to BALANCE
W (xxx) BluFiManager: Possible coexistence issue detected, applying recovery policy
```

### 3. 故障排除
如果仍有问题：
1. 检查Wi-Fi信号强度
2. 尝试降低发射功率
3. 增加重连延迟时间
4. 检查路由器兼容性

## 📝 总结

这个修复解决了BluFi中Wi-Fi和蓝牙共存的关键问题：

1. **✅ 智能共存策略**: 自动平衡Wi-Fi和蓝牙性能
2. **✅ 功率优化**: 降低发射功率减少干扰
3. **✅ 智能重连**: 考虑共存因素的重连策略
4. **✅ 故障恢复**: 自动检测和恢复共存问题
5. **✅ 诊断工具**: 提供详细的共存状态信息

通过这些改进，BluFi功能在Wi-Fi配置过程中的稳定性和成功率得到显著提升。
