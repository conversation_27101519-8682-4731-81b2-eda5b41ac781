#include "freertos_mutex_example.h"

const char* FreeRTOSTimerManager::TAG = "FRTOSTimerMgr";
const char* ReadWriteLock::TAG = "ReadWriteLock";
const char* MutexUsageExample::TAG = "MutexExample";

// ============================================================================
// FreeRTOSTimerManager 实现
// ============================================================================

FreeRTOSTimerManager::FreeRTOSTimerManager() 
    : timer1_handle_(nullptr)
    , timer2_handle_(nullptr) {
}

FreeRTOSTimerManager::~FreeRTOSTimerManager() {
    StopTimers();
}

bool FreeRTOSTimerManager::StartTimers() {
    esp_timer_create_args_t timer1_args = {
        .callback = Timer1Callback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "frtos_timer1",
        .skip_unhandled_events = false
    };
    
    esp_err_t ret = esp_timer_create(&timer1_args, &timer1_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create timer1: %s", esp_err_to_name(ret));
        return false;
    }
    
    esp_timer_create_args_t timer2_args = {
        .callback = Timer2Callback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "frtos_timer2",
        .skip_unhandled_events = false
    };
    
    ret = esp_timer_create(&timer2_args, &timer2_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create timer2: %s", esp_err_to_name(ret));
        esp_timer_delete(timer1_handle_);
        return false;
    }
    
    esp_timer_start_periodic(timer1_handle_, 2000000); // 2秒
    esp_timer_start_periodic(timer2_handle_, 3000000); // 3秒
    
    ESP_LOGI(TAG, "FreeRTOS timers started");
    return true;
}

void FreeRTOSTimerManager::StopTimers() {
    if (timer1_handle_) {
        esp_timer_stop(timer1_handle_);
        esp_timer_delete(timer1_handle_);
        timer1_handle_ = nullptr;
    }
    
    if (timer2_handle_) {
        esp_timer_stop(timer2_handle_);
        esp_timer_delete(timer2_handle_);
        timer2_handle_ = nullptr;
    }
    
    ESP_LOGI(TAG, "FreeRTOS timers stopped");
}

void FreeRTOSTimerManager::SetSharedData(std::shared_ptr<std::string> data) {
    if (shared_data_.SetResource(data, pdMS_TO_TICKS(100))) {
        ESP_LOGI(TAG, "Shared data updated successfully");
    } else {
        ESP_LOGE(TAG, "Failed to update shared data (timeout)");
    }
}

void FreeRTOSTimerManager::Timer1Callback(void* arg) {
    FreeRTOSTimerManager* manager = static_cast<FreeRTOSTimerManager*>(arg);
    manager->HandleTimer1();
}

void FreeRTOSTimerManager::Timer2Callback(void* arg) {
    FreeRTOSTimerManager* manager = static_cast<FreeRTOSTimerManager*>(arg);
    manager->HandleTimer2();
}

void FreeRTOSTimerManager::HandleTimer1() {
    // 使用短超时，避免在定时器回调中阻塞太久
    bool success = shared_data_.SafeExecute([this](std::shared_ptr<std::string> data) {
        ESP_LOGI(TAG, "Timer1 (FreeRTOS) accessing: %s", data->c_str());
        *data += " [FRTOSTimer1]";
    }, pdMS_TO_TICKS(50));
    
    if (!success) {
        ESP_LOGW(TAG, "Timer1 failed to access shared data (timeout or error)");
    }
}

void FreeRTOSTimerManager::HandleTimer2() {
    auto data = shared_data_.GetResource(pdMS_TO_TICKS(50));
    if (data) {
        ESP_LOGI(TAG, "Timer2 (FreeRTOS) accessing: %s", data->c_str());
        *data += " [FRTOSTimer2]";
        shared_data_.SetResource(data, pdMS_TO_TICKS(50));
    } else {
        ESP_LOGW(TAG, "Timer2 failed to get shared data");
    }
}

// ============================================================================
// ReadWriteLock 实现
// ============================================================================

ReadWriteLock::ReadWriteLock() : reader_count_(0) {
    read_mutex_ = xSemaphoreCreateMutex();
    write_mutex_ = xSemaphoreCreateMutex();
    resource_mutex_ = xSemaphoreCreateMutex();
    
    if (!read_mutex_ || !write_mutex_ || !resource_mutex_) {
        ESP_LOGE(TAG, "Failed to create semaphores");
    }
}

ReadWriteLock::~ReadWriteLock() {
    if (read_mutex_) vSemaphoreDelete(read_mutex_);
    if (write_mutex_) vSemaphoreDelete(write_mutex_);
    if (resource_mutex_) vSemaphoreDelete(resource_mutex_);
}

bool ReadWriteLock::AcquireReadLock(TickType_t timeout) {
    if (!read_mutex_ || !resource_mutex_) return false;
    
    if (xSemaphoreTake(read_mutex_, timeout) == pdTRUE) {
        reader_count_++;
        if (reader_count_ == 1) {
            // 第一个读者需要获取资源锁
            if (xSemaphoreTake(resource_mutex_, timeout) != pdTRUE) {
                reader_count_--;
                xSemaphoreGive(read_mutex_);
                return false;
            }
        }
        xSemaphoreGive(read_mutex_);
        return true;
    }
    return false;
}

void ReadWriteLock::ReleaseReadLock() {
    if (!read_mutex_ || !resource_mutex_) return;
    
    if (xSemaphoreTake(read_mutex_, portMAX_DELAY) == pdTRUE) {
        reader_count_--;
        if (reader_count_ == 0) {
            // 最后一个读者释放资源锁
            xSemaphoreGive(resource_mutex_);
        }
        xSemaphoreGive(read_mutex_);
    }
}

bool ReadWriteLock::AcquireWriteLock(TickType_t timeout) {
    if (!write_mutex_ || !resource_mutex_) return false;
    
    if (xSemaphoreTake(write_mutex_, timeout) == pdTRUE) {
        if (xSemaphoreTake(resource_mutex_, timeout) == pdTRUE) {
            return true;
        }
        xSemaphoreGive(write_mutex_);
    }
    return false;
}

void ReadWriteLock::ReleaseWriteLock() {
    if (!write_mutex_ || !resource_mutex_) return;
    
    xSemaphoreGive(resource_mutex_);
    xSemaphoreGive(write_mutex_);
}

// ============================================================================
// MutexUsageExample 实现
// ============================================================================

void MutexUsageExample::RunExample() {
    ESP_LOGI(TAG, "=== 开始互斥访问示例 ===");
    
    TestStdMutex();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    TestFreeRTOSMutex();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    TestReadWriteLock();
    
    ESP_LOGI(TAG, "=== 互斥访问示例完成 ===");
}

void MutexUsageExample::TestStdMutex() {
    ESP_LOGI(TAG, "--- 测试 std::mutex ---");
    
    TimerManager timer_mgr;
    auto shared_data = std::make_shared<std::string>("Initial Data");
    
    timer_mgr.SetSharedData(shared_data);
    timer_mgr.StartTimers();
    
    // 运行5秒
    vTaskDelay(pdMS_TO_TICKS(5000));
    
    timer_mgr.StopTimers();
    ESP_LOGI(TAG, "Final data: %s", shared_data->c_str());
}

void MutexUsageExample::TestFreeRTOSMutex() {
    ESP_LOGI(TAG, "--- 测试 FreeRTOS Mutex ---");
    
    FreeRTOSTimerManager timer_mgr;
    auto shared_data = std::make_shared<std::string>("FreeRTOS Data");
    
    timer_mgr.SetSharedData(shared_data);
    timer_mgr.StartTimers();
    
    // 运行5秒
    vTaskDelay(pdMS_TO_TICKS(5000));
    
    timer_mgr.StopTimers();
    ESP_LOGI(TAG, "Final FreeRTOS data: %s", shared_data->c_str());
}

void MutexUsageExample::TestReadWriteLock() {
    ESP_LOGI(TAG, "--- 测试读写锁 ---");
    
    ReadWriteResource<int> rw_resource;
    auto shared_int = std::make_shared<int>(100);
    rw_resource.SetResource(shared_int);
    
    // 模拟多个读者
    for (int i = 0; i < 3; i++) {
        rw_resource.SafeRead([&](std::shared_ptr<int> data) {
            ESP_LOGI(TAG, "Reader %d: value = %d", i, *data);
        });
    }
    
    // 模拟写者
    rw_resource.SafeWrite([&](std::shared_ptr<int>& data) {
        if (data) {
            *data = 200;
            ESP_LOGI(TAG, "Writer: updated value to %d", *data);
        }
    });
    
    // 再次读取
    rw_resource.SafeRead([&](std::shared_ptr<int> data) {
        ESP_LOGI(TAG, "Final read: value = %d", *data);
    });
}
