#include "afe_buffer_manager.h"
#include <esp_timer.h>
#include <esp_system.h>
#include <esp_heap_caps.h>
#include <algorithm>

const char* AfeBufferManager::TAG = "AfeBufferManager";

AfeBufferManager::AfeBufferManager()
    : afe_iface_(nullptr)
    , afe_data_(nullptr)
    , high_usage_threshold_(0.7f)
    , near_full_threshold_(0.9f)
    , fetch_timeout_ms_(100)
    , monitoring_interval_ms_(50)
    , adaptive_processing_enabled_(true)
    , processing_priority_(5)
    , current_state_(BufferState::NORMAL)
    , monitoring_active_(false)
    , force_stop_(false)
    , monitor_task_handle_(nullptr)
    , event_group_(nullptr)
{
    stats_mutex_ = xSemaphoreCreateMutex();
    event_group_ = xEventGroupCreate();
    last_stats_update_ = std::chrono::steady_clock::now();
    
    ESP_LOGI(TAG, "AFE Buffer Manager created");
}

AfeBufferManager::~AfeBufferManager() {
    StopMonitoring();
    
    if (stats_mutex_) {
        vSemaphoreDelete(stats_mutex_);
    }
    if (event_group_) {
        vEventGroupDelete(event_group_);
    }
    
    ESP_LOGI(TAG, "AFE Buffer Manager destroyed");
}

bool AfeBufferManager::Initialize(esp_afe_sr_iface_t* afe_iface, esp_afe_sr_data_t* afe_data) {
    if (!afe_iface || !afe_data) {
        ESP_LOGE(TAG, "Invalid AFE interface or data");
        return false;
    }
    
    afe_iface_ = afe_iface;
    afe_data_ = afe_data;
    
    ESP_LOGI(TAG, "AFE Buffer Manager initialized");
    return true;
}

void AfeBufferManager::SetBufferThresholds(float high_usage_threshold, float near_full_threshold) {
    high_usage_threshold_ = std::clamp(high_usage_threshold, 0.1f, 0.9f);
    near_full_threshold_ = std::clamp(near_full_threshold, high_usage_threshold_, 0.99f);
    
    ESP_LOGI(TAG, "Buffer thresholds set: high=%.1f%%, near_full=%.1f%%", 
             high_usage_threshold_ * 100, near_full_threshold_ * 100);
}

bool AfeBufferManager::SafeFeed(const int16_t* data) {
    if (!afe_iface_ || !afe_data_ || !data) {
        return false;
    }
    
    // 检查缓冲区状态
    BufferState state = GetBufferState();
    if (state == BufferState::OVERFLOW || state == BufferState::ERROR) {
        ESP_LOGW(TAG, "Buffer in bad state, attempting recovery");
        if (!ForceFlushBuffer()) {
            return false;
        }
    }
    
    // 执行feed操作
    auto start_time = esp_timer_get_time();
    int result = afe_iface_->feed(afe_data_, data);
    auto end_time = esp_timer_get_time();
    
    // 更新统计
    if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(10)) == pdTRUE) {
        stats_.total_feeds++;
        if (result != 0) {
            ESP_LOGW(TAG, "Feed operation failed with result: %d", result);
        }
        xSemaphoreGive(stats_mutex_);
    }
    
    return result == 0;
}

bool AfeBufferManager::SafeFetch(afe_fetch_result_t** result, uint32_t timeout_ms) {
    if (!afe_iface_ || !afe_data_ || !result) {
        return false;
    }
    
    auto start_time = esp_timer_get_time();
    
    // 使用指定的超时时间，如果为0则使用默认值
    uint32_t actual_timeout = (timeout_ms == 0) ? fetch_timeout_ms_ : timeout_ms;
    
    *result = afe_iface_->fetch_with_delay(afe_data_, pdMS_TO_TICKS(actual_timeout));
    
    auto end_time = esp_timer_get_time();
    uint32_t fetch_time_us = (uint32_t)(end_time - start_time);
    
    // 更新统计
    if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(10)) == pdTRUE) {
        stats_.total_fetches++;
        
        if (*result == nullptr || (*result)->ret_value == ESP_FAIL) {
            stats_.fetch_failures++;
        } else {
            // 更新fetch时间统计
            stats_.avg_fetch_time_us = (stats_.avg_fetch_time_us * (stats_.total_fetches - 1) + fetch_time_us) / stats_.total_fetches;
            stats_.max_fetch_time_us = std::max(stats_.max_fetch_time_us, fetch_time_us);
        }
        
        xSemaphoreGive(stats_mutex_);
    }
    
    bool success = (*result != nullptr && (*result)->ret_value == ESP_OK);
    
    if (!success) {
        ESP_LOGD(TAG, "Fetch failed or timed out after %lu ms", actual_timeout);
    }
    
    return success;
}

bool AfeBufferManager::ForceFlushBuffer() {
    if (!afe_iface_ || !afe_data_) {
        return false;
    }
    
    ESP_LOGW(TAG, "Force flushing AFE buffer");
    
    // 尝试快速消费缓冲区中的数据
    int flush_count = 0;
    const int max_flush_attempts = 10;
    
    while (flush_count < max_flush_attempts) {
        auto result = afe_iface_->fetch_with_delay(afe_data_, pdMS_TO_TICKS(10));
        if (result == nullptr || result->ret_value == ESP_FAIL) {
            break;
        }
        flush_count++;
    }
    
    ESP_LOGI(TAG, "Flushed %d items from AFE buffer", flush_count);
    
    // 更新统计
    if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(10)) == pdTRUE) {
        stats_.buffer_overflows++;
        xSemaphoreGive(stats_mutex_);
    }
    
    return flush_count > 0;
}

bool AfeBufferManager::ResetBuffer() {
    if (!afe_iface_ || !afe_data_) {
        return false;
    }
    
    ESP_LOGI(TAG, "Resetting AFE buffer");
    
    int result = afe_iface_->reset_buffer(afe_data_);
    
    if (result == ESP_OK) {
        // 重置统计数据
        if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(100)) == pdTRUE) {
            stats_ = BufferStats(); // 重置为默认值
            xSemaphoreGive(stats_mutex_);
        }
        
        current_state_ = BufferState::NORMAL;
        ESP_LOGI(TAG, "AFE buffer reset successfully");
        return true;
    } else {
        ESP_LOGE(TAG, "Failed to reset AFE buffer: %d", result);
        return false;
    }
}

AfeBufferManager::BufferStats AfeBufferManager::GetStats() const {
    BufferStats stats;
    
    if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(100)) == pdTRUE) {
        stats = stats_;
        stats.buffer_usage_percent = EstimateBufferUsage();
        stats.is_healthy = IsBufferHealthy();
        xSemaphoreGive(stats_mutex_);
    }
    
    return stats;
}

AfeBufferManager::BufferState AfeBufferManager::GetBufferState() const {
    return current_state_.load();
}

float AfeBufferManager::GetBufferUsagePercent() const {
    return EstimateBufferUsage() / 100.0f;
}

bool AfeBufferManager::IsBufferHealthy() const {
    BufferState state = current_state_.load();

    if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(10)) == pdTRUE) {
        bool healthy = (state == BufferState::NORMAL || state == BufferState::HIGH_USAGE) &&
                      (stats_.fetch_failures < stats_.total_fetches * 0.1f) && // 失败率小于10%
                      (stats_.buffer_overflows == 0 || stats_.total_feeds > 0);
        xSemaphoreGive(stats_mutex_);
        return healthy;
    }

    return state == BufferState::NORMAL;
}

void AfeBufferManager::EnableAdaptiveProcessing(bool enable) {
    adaptive_processing_enabled_ = enable;
    ESP_LOGI(TAG, "Adaptive processing %s", enable ? "enabled" : "disabled");

    if (enable) {
        ESP_LOGI(TAG, "Adaptive processing will automatically adjust:");
        ESP_LOGI(TAG, "- Monitoring interval based on buffer state");
        ESP_LOGI(TAG, "- Processing strategy based on performance");
        ESP_LOGI(TAG, "- Recovery actions based on overflow conditions");
    } else {
        ESP_LOGI(TAG, "Using fixed processing parameters");
    }
}

void AfeBufferManager::SetProcessingPriority(UBaseType_t priority) {
    processing_priority_ = priority;
    ESP_LOGI(TAG, "Processing priority set to %u", priority);

    // 如果监控任务正在运行，更新其优先级
    if (monitor_task_handle_ != nullptr) {
        vTaskPrioritySet(monitor_task_handle_, priority);
        ESP_LOGI(TAG, "Updated running monitor task priority to %u", priority);
    }
}

void AfeBufferManager::OptimizeForLowLatency() {
    ESP_LOGI(TAG, "Optimizing for low latency");

    // 设置低延迟参数
    SetBufferThresholds(0.5f, 0.7f);        // 更低的阈值
    SetFetchTimeout(20);                     // 很短的超时
    SetMonitoringInterval(10);               // 高频监控
    SetProcessingPriority(8);                // 高优先级
    EnableAdaptiveProcessing(true);          // 启用自适应

    ESP_LOGI(TAG, "Low latency optimization applied:");
    ESP_LOGI(TAG, "- Buffer thresholds: 50%% / 70%%");
    ESP_LOGI(TAG, "- Fetch timeout: 20ms");
    ESP_LOGI(TAG, "- Monitor interval: 10ms");
    ESP_LOGI(TAG, "- Task priority: 8");
}

void AfeBufferManager::OptimizeForStability() {
    ESP_LOGI(TAG, "Optimizing for stability");

    // 设置稳定性参数
    SetBufferThresholds(0.8f, 0.95f);       // 较高的阈值
    SetFetchTimeout(200);                    // 较长的超时
    SetMonitoringInterval(100);              // 较低频率的监控
    SetProcessingPriority(5);                // 中等优先级
    EnableAdaptiveProcessing(false);         // 禁用自适应

    ESP_LOGI(TAG, "Stability optimization applied:");
    ESP_LOGI(TAG, "- Buffer thresholds: 80%% / 95%%");
    ESP_LOGI(TAG, "- Fetch timeout: 200ms");
    ESP_LOGI(TAG, "- Monitor interval: 100ms");
    ESP_LOGI(TAG, "- Task priority: 5");
    ESP_LOGI(TAG, "- Adaptive processing: disabled");
}

void AfeBufferManager::SetFetchTimeout(uint32_t timeout_ms) {
    fetch_timeout_ms_ = timeout_ms;
    ESP_LOGI(TAG, "Fetch timeout set to %lu ms", timeout_ms);
}

void AfeBufferManager::SetMonitoringInterval(uint32_t interval_ms) {
    monitoring_interval_ms_ = interval_ms;
    ESP_LOGI(TAG, "Monitoring interval set to %lu ms", interval_ms);
}

void AfeBufferManager::OnBufferOverflow(std::function<void(const BufferStats&)> callback) {
    overflow_callback_ = callback;
    ESP_LOGI(TAG, "Buffer overflow callback registered");
}

void AfeBufferManager::OnBufferStateChange(std::function<void(BufferState, BufferState)> callback) {
    state_change_callback_ = callback;
    ESP_LOGI(TAG, "Buffer state change callback registered");
}

void AfeBufferManager::OnPerformanceAlert(std::function<void(const std::string&)> callback) {
    performance_alert_callback_ = callback;
    ESP_LOGI(TAG, "Performance alert callback registered");
}

bool AfeBufferManager::IsMonitoring() const {
    return monitoring_active_.load();
}

void AfeBufferManager::StartMonitoring() {
    if (monitoring_active_.load()) {
        ESP_LOGW(TAG, "Monitoring already active");
        return;
    }
    
    force_stop_ = false;
    monitoring_active_ = true;
    
    xTaskCreate(MonitorTaskWrapper, "afe_buffer_monitor", 4096, this, processing_priority_, &monitor_task_handle_);
    
    ESP_LOGI(TAG, "AFE buffer monitoring started");
}

void AfeBufferManager::StopMonitoring() {
    if (!monitoring_active_.load()) {
        return;
    }
    
    force_stop_ = true;
    monitoring_active_ = false;
    
    if (monitor_task_handle_) {
        vTaskDelete(monitor_task_handle_);
        monitor_task_handle_ = nullptr;
    }
    
    ESP_LOGI(TAG, "AFE buffer monitoring stopped");
}

void AfeBufferManager::MonitorTaskWrapper(void* param) {
    auto* manager = static_cast<AfeBufferManager*>(param);
    manager->MonitorTask();
}

void AfeBufferManager::MonitorTask() {
    ESP_LOGI(TAG, "AFE buffer monitor task started");
    
    while (!force_stop_.load()) {
        UpdateStats();
        CheckBufferHealth();
        
        if (adaptive_processing_enabled_) {
            AdjustProcessingStrategy();
        }
        
        vTaskDelay(pdMS_TO_TICKS(monitoring_interval_ms_));
    }
    
    ESP_LOGI(TAG, "AFE buffer monitor task stopped");
}

uint32_t AfeBufferManager::EstimateBufferUsage() const {
    // 这是一个估算方法，基于feed和fetch的比率
    if (stats_.total_feeds == 0) {
        return 0;
    }
    
    // 计算积压的数据量
    int32_t backlog = static_cast<int32_t>(stats_.total_feeds) - static_cast<int32_t>(stats_.total_fetches);
    
    if (backlog <= 0) {
        return 0;
    }
    
    // 假设缓冲区最大容量为100个数据块（这个值可能需要根据实际AFE配置调整）
    const uint32_t estimated_max_capacity = 100;
    uint32_t usage_percent = (static_cast<uint32_t>(backlog) * 100) / estimated_max_capacity;
    
    return std::min(usage_percent, 100lu);
}

void AfeBufferManager::UpdateStats() {
    if (xSemaphoreTake(stats_mutex_, pdMS_TO_TICKS(10)) == pdTRUE) {
        stats_.buffer_usage_percent = EstimateBufferUsage();
        stats_.is_healthy = IsBufferHealthy();
        xSemaphoreGive(stats_mutex_);
    }
}

void AfeBufferManager::CheckBufferHealth() {
    BufferState old_state = current_state_.load();
    BufferState new_state = DetermineBufferState();

    if (old_state != new_state) {
        current_state_ = new_state;
        NotifyStateChange(new_state);

        if (new_state == BufferState::OVERFLOW) {
            HandleBufferOverflow();
        }
    }
}

AfeBufferManager::BufferState AfeBufferManager::DetermineBufferState() const {
    uint32_t usage = EstimateBufferUsage();

    if (usage >= near_full_threshold_ * 100) {
        return BufferState::OVERFLOW;
    } else if (usage >= high_usage_threshold_ * 100) {
        return BufferState::NEAR_FULL;
    } else if (usage >= 50) {
        return BufferState::HIGH_USAGE;
    } else {
        return BufferState::NORMAL;
    }
}

void AfeBufferManager::NotifyStateChange(BufferState new_state) {
    const char* state_names[] = {"NORMAL", "HIGH_USAGE", "NEAR_FULL", "OVERFLOW", "ERROR"};
    ESP_LOGI(TAG, "Buffer state changed to: %s", state_names[static_cast<int>(new_state)]);

    if (state_change_callback_) {
        state_change_callback_(current_state_.load(), new_state);
    }
}

void AfeBufferManager::HandleBufferOverflow() {
    ESP_LOGW(TAG, "Buffer overflow detected, applying recovery measures");

    // 触发溢出回调
    if (overflow_callback_) {
        BufferStats stats = GetStats();
        overflow_callback_(stats);
    }

    // 尝试恢复
    if (!ForceFlushBuffer()) {
        ESP_LOGE(TAG, "Failed to recover from buffer overflow");
        current_state_ = BufferState::ERROR;
    }
}

void AfeBufferManager::AdjustProcessingStrategy() {
    BufferState state = current_state_.load();

    switch (state) {
        case BufferState::HIGH_USAGE:
            // 提高处理频率
            monitoring_interval_ms_ = std::max(monitoring_interval_ms_ - 5, 10lu);
            break;

        case BufferState::NEAR_FULL:
            // 进一步提高处理频率
            monitoring_interval_ms_ = std::max(monitoring_interval_ms_ - 10, 5lu);
            break;

        case BufferState::OVERFLOW:
            // 最高处理频率
            monitoring_interval_ms_ = 5;
            break;

        case BufferState::NORMAL:
            // 恢复正常频率
            monitoring_interval_ms_ = std::min(monitoring_interval_ms_ + 5, 50lu);
            break;

        default:
            break;
    }
}


// ============================================================================
// 工厂方法实现
// ============================================================================

std::unique_ptr<AfeBufferManager> AfeBufferManagerFactory::CreateForWakeWord() {
    auto manager = std::make_unique<AfeBufferManager>();
    manager->SetBufferThresholds(0.6f, 0.8f);  // 较低的阈值，更敏感
    manager->SetFetchTimeout(50);               // 较短的超时
    manager->SetMonitoringInterval(30);         // 更频繁的监控
    manager->SetProcessingPriority(7);          // 高优先级
    manager->EnableAdaptiveProcessing(true);    // 启用自适应
    ESP_LOGI("AfeBufferManagerFactory", "Created wake word optimized buffer manager");
    return manager;
}

std::unique_ptr<AfeBufferManager> AfeBufferManagerFactory::CreateForVoiceProcessing() {
    auto manager = std::make_unique<AfeBufferManager>();
    manager->SetBufferThresholds(0.7f, 0.9f);  // 标准阈值
    manager->SetFetchTimeout(100);              // 标准超时
    manager->SetMonitoringInterval(50);         // 标准监控频率
    manager->SetProcessingPriority(6);          // 中高优先级
    manager->EnableAdaptiveProcessing(true);    // 启用自适应
    ESP_LOGI("AfeBufferManagerFactory", "Created voice processing optimized buffer manager");
    return manager;
}

std::unique_ptr<AfeBufferManager> AfeBufferManagerFactory::CreateForLowLatency() {
    auto manager = std::make_unique<AfeBufferManager>();
    manager->OptimizeForLowLatency();           // 使用专门的低延迟优化
    ESP_LOGI("AfeBufferManagerFactory", "Created low latency optimized buffer manager");
    return manager;
}

std::unique_ptr<AfeBufferManager> AfeBufferManagerFactory::CreateForStability() {
    auto manager = std::make_unique<AfeBufferManager>();
    manager->OptimizeForStability();            // 使用专门的稳定性优化
    ESP_LOGI("AfeBufferManagerFactory", "Created stability optimized buffer manager");
    return manager;
}

std::unique_ptr<AfeBufferManager> AfeBufferManagerFactory::CreateForHardware(
    size_t available_ram_kb,
    uint32_t cpu_freq_mhz,
    bool has_psram) {

    auto manager = std::make_unique<AfeBufferManager>();

    ESP_LOGI("AfeBufferManagerFactory", "Creating buffer manager for hardware:");
    ESP_LOGI("AfeBufferManagerFactory", "- Available RAM: %zu KB", available_ram_kb);
    ESP_LOGI("AfeBufferManagerFactory", "- CPU Frequency: %lu MHz", cpu_freq_mhz);
    ESP_LOGI("AfeBufferManagerFactory", "- PSRAM: %s", has_psram ? "Yes" : "No");

    // 根据硬件配置调整参数
    if (available_ram_kb < 200) {
        // 低内存配置
        ESP_LOGI("AfeBufferManagerFactory", "Low memory configuration detected");
        manager->OptimizeForStability();
        manager->SetMonitoringInterval(200);  // 降低监控频率
    } else if (available_ram_kb > 500 && cpu_freq_mhz > 200) {
        // 高性能配置
        ESP_LOGI("AfeBufferManagerFactory", "High performance configuration detected");
        manager->OptimizeForLowLatency();
    } else {
        // 标准配置
        ESP_LOGI("AfeBufferManagerFactory", "Standard configuration detected");
        manager->SetBufferThresholds(0.7f, 0.9f);
        manager->SetFetchTimeout(100);
        manager->SetMonitoringInterval(50);
        manager->SetProcessingPriority(6);
        manager->EnableAdaptiveProcessing(true);
    }

    // PSRAM优化
    if (has_psram) {
        ESP_LOGI("AfeBufferManagerFactory", "PSRAM detected, enabling extended buffering");
        // 可以设置更大的缓冲区阈值
        manager->SetBufferThresholds(0.8f, 0.95f);
    }

    ESP_LOGI("AfeBufferManagerFactory", "Hardware-optimized buffer manager created");
    return manager;
}

// ============================================================================
// 性能调优助手实现
// ============================================================================

std::vector<AfePerformanceTuner::TuningRecommendation> AfePerformanceTuner::AnalyzePerformance(
    const AfeBufferManager::BufferStats& stats) {

    std::vector<TuningRecommendation> recommendations;

    // 检查缓冲区溢出
    if (stats.buffer_overflows > 0) {
        recommendations.push_back({
            "Buffer overflows detected",
            "Increase fetch frequency or reduce feed rate",
            9,
            true
        });
    }

    // 检查fetch失败率
    if (stats.total_fetches > 0) {
        float failure_rate = static_cast<float>(stats.fetch_failures) / stats.total_fetches;
        if (failure_rate > 0.1f) {
            recommendations.push_back({
                "High fetch failure rate: " + std::to_string(failure_rate * 100) + "%",
                "Increase fetch timeout or check AFE configuration",
                8,
                failure_rate > 0.2f
            });
        }
    }

    // 检查平均fetch时间
    if (stats.avg_fetch_time_us > 50000) { // 50ms
        recommendations.push_back({
            "High average fetch time: " + std::to_string(stats.avg_fetch_time_us / 1000) + "ms",
            "Optimize AFE processing or increase task priority",
            7,
            stats.avg_fetch_time_us > 100000
        });
    }

    // 检查缓冲区使用率
    if (stats.buffer_usage_percent > 80) {
        recommendations.push_back({
            "High buffer usage: " + std::to_string(stats.buffer_usage_percent) + "%",
            "Increase processing frequency or reduce input rate",
            6,
            stats.buffer_usage_percent > 95
        });
    }

    return recommendations;
}

void AfePerformanceTuner::ApplyRecommendations(
    AfeBufferManager& manager,
    const std::vector<TuningRecommendation>& recommendations) {

    ESP_LOGI("AfePerformanceTuner", "Applying %zu performance recommendations", recommendations.size());

    for (const auto& rec : recommendations) {
        ESP_LOGI("AfePerformanceTuner", "Applying [Priority %d]: %s", rec.priority, rec.description.c_str());

        // 根据推荐内容应用相应的优化
        if (rec.description.find("Buffer overflows") != std::string::npos) {
            // 缓冲区溢出问题
            manager.SetBufferThresholds(0.5f, 0.7f);  // 降低阈值
            manager.SetMonitoringInterval(20);         // 增加监控频率
            ESP_LOGI("AfePerformanceTuner", "Applied: Lower buffer thresholds and increased monitoring");

        } else if (rec.description.find("High fetch failure rate") != std::string::npos) {
            // 高fetch失败率
            manager.SetFetchTimeout(200);              // 增加超时时间
            ESP_LOGI("AfePerformanceTuner", "Applied: Increased fetch timeout");

        } else if (rec.description.find("High average fetch time") != std::string::npos) {
            // 高平均fetch时间
            manager.SetProcessingPriority(8);          // 提高优先级
            manager.OptimizeForLowLatency();           // 应用低延迟优化
            ESP_LOGI("AfePerformanceTuner", "Applied: Increased priority and low latency optimization");

        } else if (rec.description.find("High buffer usage") != std::string::npos) {
            // 高缓冲区使用率
            manager.SetMonitoringInterval(30);         // 增加处理频率
            manager.EnableAdaptiveProcessing(true);    // 启用自适应处理
            ESP_LOGI("AfePerformanceTuner", "Applied: Increased processing frequency and adaptive processing");
        }

        if (rec.critical) {
            ESP_LOGW("AfePerformanceTuner", "CRITICAL issue addressed: %s", rec.description.c_str());
        }
    }

    ESP_LOGI("AfePerformanceTuner", "All recommendations applied successfully");
}

std::string AfePerformanceTuner::GeneratePerformanceReport(
    const AfeBufferManager::BufferStats& stats) {

    std::string report = "=== AFE Performance Report ===\n";
    report += "Total Feeds: " + std::to_string(stats.total_feeds) + "\n";
    report += "Total Fetches: " + std::to_string(stats.total_fetches) + "\n";
    report += "Buffer Overflows: " + std::to_string(stats.buffer_overflows) + "\n";
    report += "Fetch Failures: " + std::to_string(stats.fetch_failures) + "\n";
    report += "Avg Fetch Time: " + std::to_string(stats.avg_fetch_time_us / 1000) + "ms\n";
    report += "Max Fetch Time: " + std::to_string(stats.max_fetch_time_us / 1000) + "ms\n";
    report += "Buffer Usage: " + std::to_string(stats.buffer_usage_percent) + "%\n";
    report += "Health Status: " + std::string(stats.is_healthy ? "Healthy" : "Unhealthy") + "\n";

    auto recommendations = AnalyzePerformance(stats);
    if (!recommendations.empty()) {
        report += "\n=== Recommendations ===\n";
        for (const auto& rec : recommendations) {
            report += "[Priority " + std::to_string(rec.priority) + "] ";
            if (rec.critical) report += "[CRITICAL] ";
            report += rec.description + " -> " + rec.action + "\n";
        }
    }

    return report;
}
