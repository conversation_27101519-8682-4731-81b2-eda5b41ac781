#ifndef FREERTOS_MUTEX_EXAMPLE_H
#define FREERTOS_MUTEX_EXAMPLE_H

#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <freertos/task.h>
#include <esp_timer.h>
#include <esp_log.h>
#include <memory>

/**
 * 使用FreeRTOS信号量的线程安全资源管理器
 * 适用于ESP32环境，性能更好
 */
template<typename T>
class FreeRTOSThreadSafeResource {
public:
    FreeRTOSThreadSafeResource() {
        mutex_ = xSemaphoreCreateMutex();
        if (!mutex_) {
            ESP_LOGE("ThreadSafeResource", "Failed to create mutex");
        }
    }
    
    ~FreeRTOSThreadSafeResource() {
        if (mutex_) {
            vSemaphoreDelete(mutex_);
        }
    }
    
    // 禁用拷贝构造和赋值
    FreeRTOSThreadSafeResource(const FreeRTOSThreadSafeResource&) = delete;
    FreeRTOSThreadSafeResource& operator=(const FreeRTOSThreadSafeResource&) = delete;
    
    // 安全地设置资源
    bool SetResource(std::shared_ptr<T> resource, TickType_t timeout = portMAX_DELAY) {
        if (!mutex_) return false;
        
        if (xSemaphoreTake(mutex_, timeout) == pdTRUE) {
            resource_ = resource;
            xSemaphoreGive(mutex_);
            return true;
        }
        return false;
    }
    
    // 安全地获取资源
    std::shared_ptr<T> GetResource(TickType_t timeout = portMAX_DELAY) {
        if (!mutex_) return nullptr;
        
        std::shared_ptr<T> result;
        if (xSemaphoreTake(mutex_, timeout) == pdTRUE) {
            result = resource_;
            xSemaphoreGive(mutex_);
        }
        return result;
    }
    
    // 安全地执行操作
    template<typename Func>
    bool SafeExecute(Func&& func, TickType_t timeout = portMAX_DELAY) {
        if (!mutex_) return false;
        
        if (xSemaphoreTake(mutex_, timeout) == pdTRUE) {
            if (resource_) {
                func(resource_);
            }
            xSemaphoreGive(mutex_);
            return true;
        }
        return false;
    }
    
    // 检查资源是否存在
    bool IsValid(TickType_t timeout = portMAX_DELAY) {
        if (!mutex_) return false;
        
        bool valid = false;
        if (xSemaphoreTake(mutex_, timeout) == pdTRUE) {
            valid = (resource_ != nullptr);
            xSemaphoreGive(mutex_);
        }
        return valid;
    }
    
    // 重置资源
    bool Reset(TickType_t timeout = portMAX_DELAY) {
        if (!mutex_) return false;
        
        if (xSemaphoreTake(mutex_, timeout) == pdTRUE) {
            resource_.reset();
            xSemaphoreGive(mutex_);
            return true;
        }
        return false;
    }

private:
    std::shared_ptr<T> resource_;
    SemaphoreHandle_t mutex_;
};

/**
 * 使用FreeRTOS信号量的定时器管理器
 */
class FreeRTOSTimerManager {
public:
    FreeRTOSTimerManager();
    ~FreeRTOSTimerManager();
    
    bool StartTimers();
    void StopTimers();
    void SetSharedData(std::shared_ptr<std::string> data);
    
private:
    FreeRTOSThreadSafeResource<std::string> shared_data_;
    esp_timer_handle_t timer1_handle_;
    esp_timer_handle_t timer2_handle_;
    
    static void Timer1Callback(void* arg);
    static void Timer2Callback(void* arg);
    
    void HandleTimer1();
    void HandleTimer2();
    
    static const char* TAG;
};

/**
 * 读写锁实现（适用于读多写少的场景）
 */
class ReadWriteLock {
public:
    ReadWriteLock();
    ~ReadWriteLock();
    
    // 获取读锁
    bool AcquireReadLock(TickType_t timeout = portMAX_DELAY);
    void ReleaseReadLock();
    
    // 获取写锁
    bool AcquireWriteLock(TickType_t timeout = portMAX_DELAY);
    void ReleaseWriteLock();

private:
    SemaphoreHandle_t read_mutex_;      // 保护读者计数
    SemaphoreHandle_t write_mutex_;     // 写者互斥
    SemaphoreHandle_t resource_mutex_;  // 资源访问互斥
    int reader_count_;
    
    static const char* TAG;
};

/**
 * 使用读写锁的资源管理器
 */
template<typename T>
class ReadWriteResource {
public:
    ReadWriteResource() = default;
    
    // 读操作
    template<typename Func>
    bool SafeRead(Func&& func, TickType_t timeout = portMAX_DELAY) {
        if (rw_lock_.AcquireReadLock(timeout)) {
            if (resource_) {
                func(resource_);
            }
            rw_lock_.ReleaseReadLock();
            return true;
        }
        return false;
    }
    
    // 写操作
    template<typename Func>
    bool SafeWrite(Func&& func, TickType_t timeout = portMAX_DELAY) {
        if (rw_lock_.AcquireWriteLock(timeout)) {
            func(resource_);
            rw_lock_.ReleaseWriteLock();
            return true;
        }
        return false;
    }
    
    // 设置资源（写操作）
    bool SetResource(std::shared_ptr<T> resource, TickType_t timeout = portMAX_DELAY) {
        return SafeWrite([this, resource](std::shared_ptr<T>&) {
            resource_ = resource;
        }, timeout);
    }

private:
    std::shared_ptr<T> resource_;
    ReadWriteLock rw_lock_;
};

/**
 * 使用示例类
 */
class MutexUsageExample {
public:
    static void RunExample();

private:
    static void TestStdMutex();
    static void TestFreeRTOSMutex();
    static void TestReadWriteLock();

    static const char* TAG;
};

#endif // FREERTOS_MUTEX_EXAMPLE_H
