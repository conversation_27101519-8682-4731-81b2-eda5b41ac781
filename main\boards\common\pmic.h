#pragma once

#include <esp_adc/adc_oneshot.h>
#include <esp_adc/adc_cali.h>
#include <esp_timer.h>
#include <esp_log.h>
#include <lvgl.h>
#include <functional>
#include <driver/gpio.h>

#define BSP_BAT_ADC_CHAN  (ADC_CHANNEL_6)    // GPIO17
#define BSP_BAT_ADC_ATTEN (ADC_ATTEN_DB_2_5) // 0 ~ 1100 mV
#define BSP_BAT_VOL_RATIO ((62 + 20) / 20)

/**
 * @brief 电池管理类 - 基于ESP-ADC的PMIC实现
 *
 * 功能特性：
 * - 电池电压和电量监测
 * - 充电状态检测
 * - USB电源检测
 * - 电源管理和控制
 * - 温度监测（如果硬件支持）
 */
class Pmic {
public:
    // 构造函数
    Pmic(int charging_pin);
    // 析构函数
    ~Pmic();

    // 设置电池阈值
    void SetBatteryThresholds(float low_threshold, float critical_threshold);

    // 充电状态检测
    bool IsCharging();
    // 放电状态检测
    bool IsDischarging();
    // 充电完成检测
    bool IsChargingDone();
    // 获取电池电压 (V)
    float GetBatteryVoltage();
    // 获取电池电量等级 (0-100)
    int GetBatteryLevel();
    // 获取温度 (模拟实现，实际硬件可能需要温度传感器)
    float GetTemperature();
    // 关机功能 (软件关机)
    void PowerOff();

    // 新增电源检测方法
    // 电池存在检测
    bool IsBatteryPresent();
    // 获取电源来源
    // 0=电池, 1=USB, 2=两者都有
    int GetPowerSource();

    // 电池健康状态检测
    bool IsBatteryHealthy();
    // 获取电池健康度
    // 返回0.0-1.0的健康度
    float GetBatteryHealth();

    // 电源管理
    // 启用/禁用低功耗模式
    void EnableLowPowerMode(bool enable);
    // 检查是否处于低功耗模式
    bool IsLowPowerMode();

    // 统计信息
    // 电源统计信息结构体
    struct PowerStats {
        uint32_t total_charge_cycles = 0;
        uint32_t total_runtime_minutes = 0;
        float avg_battery_voltage = 0.0f;
        float min_battery_voltage = 5.0f;
        float max_battery_voltage = 0.0f;
        uint32_t low_battery_events = 0;
        uint32_t charging_events = 0;
    };

    // 电源统计信息相关方法
    // 获取电源统计信息
    PowerStats GetPowerStats();
    // 重置电源统计信息
    void ResetPowerStats();

    /**
     * @brief  Clear interrupt controller state.
     */
    void clearIrqStatus()
    {
    }

    bool isPekeyShortPressIrq(void)
    {
        return false;
    }

    bool isPekeyLongPressIrq(void)
    {
        return false;
    }

    // 电池参数配置
    static constexpr float BATTERY_MIN_VOLTAGE = 3.0f;      // 最低电压 (V), 低于该值需要睡眠
    static constexpr float BATTERY_MAX_VOLTAGE = 5.5f;      // 最高电压 (V)
    static constexpr float BATTERY_NOMINAL_VOLTAGE = 3.7f;  // 标称电压 (V)
    static constexpr float BATTERY_LOW_VOLTAGE = 3.2f;      // 低电压 (V), 低于该值需要充电
    static constexpr int BATTERY_NOMINAL_LEVEL = 50;        // 标称电量 (0-100), 超过该值表示正常, 不需要充电/睡眠
    static constexpr int BATTERY_LOW_LEVEL = 20;            // 低电量 (0-100), 低于该值需要充电
    static constexpr int BATTERY_MIN_LEVEL = 10;            // 低电量 (0-100), 低于该值需要睡眠

private:
    // ADC相关
    adc_oneshot_unit_handle_t adc_handle_ = nullptr;
    adc_cali_handle_t cali_handle_ = nullptr;
    bool adc_initialized_ = false;

    // GPIO引脚配置
    int charging_pin_;

    // 电源管理状态
    // 低功耗模式
    bool low_power_mode_ = false;
    // 低电量电池阈值
    float low_battery_threshold_ = 3.4f;
    // 临界电量阈值
    float critical_battery_threshold_ = BATTERY_MIN_VOLTAGE;

    // 统计数据
    PowerStats power_stats_;
    // 上次更新统计信息的时间
    uint32_t last_update_time_ = 0;

    // 内部方法
    // 初始化ADC
    bool InitializeADC();
    // 反初始化ADC
    void DeInitializeADC();

    // USB连接检测
    bool IsUsbConnected();
    // USB供电检测
    bool IsUsbPowered();

    // 更新电源统计信息
    void UpdatePowerStats();

    // 电池电量百分比计算方法
    // print参数控制是否打印电池电量百分比
    uint8_t GetBatteryPercent();

    // 获取电池电压 (mV)
    uint16_t GetBatteryVoltageMV(void);

};
