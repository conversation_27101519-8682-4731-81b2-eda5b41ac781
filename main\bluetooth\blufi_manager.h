#pragma once

#include <esp_log.h>
#include <esp_blufi_api.h>
#include <functional>
#include <esp_wifi.h>
#include <cstdint>
#include "blufi_common.h"

/**
 * @brief BluFi管理器 - 通过BLE配置WiFi
 */
class BluFiManager {
public:
    enum class State {
        DISABLED,
        IDLE,
        ADVERTISING,
        CONNECTED,
        CONFIGURING,
        CONNECTED_TO_WIFI
    };

    struct WifiConfig {
        char ssid[32];
        char password[64];
        bool is_configured;
    };

    using StateCallback = std::function<void(State state, bool enabled)>;
    using WifiConfigCallback = std::function<void(const WifiConfig& config)>;
    using StatusCallback = std::function<void(const char* message)>;

    BluFiManager();
    ~BluFiManager();

    bool Enable();
    bool Disable();
    
    State GetState() const { return current_state_; }
    bool IsEnabled() const { return is_initialized_; }
    WifiConfig GetWifiConfig() const { return wifi_config_; }
    
    void SetStateCallback(StateCallback callback) { state_callback_ = callback; }
    void SetWifiConfigCallback(WifiConfigCallback callback) { wifi_config_callback_ = callback; }
    void SetStatusCallback(StatusCallback callback) { status_callback_ = callback; }
    
    // WiFi状态报告
    void ReportWifiStatus(wifi_event_t event);
    void ReportWifiConnected(const char* ip);
    void ReportWifiDisconnected();

private:
    // BluFi事件处理
    static void BluFiEventCallback(esp_blufi_cb_event_t event, esp_blufi_cb_param_t* param);
    void HandleBluFiEvent(esp_blufi_cb_event_t event, esp_blufi_cb_param_t* param);
    
    // 状态管理
    void UpdateState(State new_state, const char * msg);
    void UpdateWifiConfig(const char* ssid, size_t ssid_len, const char* password, size_t pass_len);
    
    // 安全相关
    void HandleSecurityInit();
    void HandleDataReceived(uint8_t* data, int len);

private:
	State current_state_;
    WifiConfig wifi_config_;
    bool is_initialized_;

    bool Initialize();
	void InitialiseWifi(void);
	int SoftapGetCurrentConnectionNumber(void);
	void BlufiRecordWifiConnInfo(int rssi, uint8_t reason);
	void BlufiWifiConnect(void);
	bool BlufiWifiReconnect(void);
	static void IpEventHandler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data);
	static void WifiEventHandler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data);

	/* FreeRTOS event group to signal when we are connected & ready to make a request */
	EventGroupHandle_t wifi_event_group_;
	wifi_config_t ap_config_;
	wifi_config_t sta_config_;
	bool ble_is_connected_ = false;
	bool gl_sta_connected_ = false;
	bool gl_sta_is_connecting_ = false;
	uint8_t gl_sta_bssid_[6];
	uint8_t gl_sta_ssid[32];
	uint8_t gl_sta_ssid_len_ = 0;
	esp_blufi_extra_info_t gl_sta_conn_info_;
    bool gl_sta_got_ip_ = false;
	uint8_t blufi_wifi_retry_ = 0;

    StateCallback state_callback_;
    WifiConfigCallback wifi_config_callback_;
    StatusCallback status_callback_;
    
    static BluFiManager* instance_;
    static const char* TAG;
};

/**
 * @brief 获取状态名称
 */
const char* GetBluFiStateName(BluFiManager::State state);

/**
 * @brief BluFi配置指南
 */
class BluFiGuide {
public:
    static void ShowBluFiInfo() {
        ESP_LOGI("BluFiGuide", "=== BluFi WiFi配置功能 ===");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "📱 功能特点:");
        ESP_LOGI("BluFiGuide", "- 通过BLE配置WiFi");
        ESP_LOGI("BluFiGuide", "- 无需预先连接WiFi");
        ESP_LOGI("BluFiGuide", "- 支持加密传输");
        ESP_LOGI("BluFiGuide", "- 自动保存配置");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "📲 使用方法:");
        ESP_LOGI("BluFiGuide", "1. 下载EspBlufi App");
        ESP_LOGI("BluFiGuide", "2. 扫描并连接设备");
        ESP_LOGI("BluFiGuide", "3. 输入WiFi信息");
        ESP_LOGI("BluFiGuide", "4. 等待配置完成");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "🔧 支持的App:");
        ESP_LOGI("BluFiGuide", "- EspBlufi (官方)");
        ESP_LOGI("BluFiGuide", "- ESP32 BluFi");
        ESP_LOGI("BluFiGuide", "- 其他兼容BluFi协议的App");
        ESP_LOGI("BluFiGuide", "");
    }
    
    static void ShowConfigurationSteps() {
        ESP_LOGI("BluFiGuide", "=== WiFi配置步骤 ===");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "📋 配置流程:");
        ESP_LOGI("BluFiGuide", "1. 设备开始BluFi广播");
        ESP_LOGI("BluFiGuide", "2. 手机连接到设备");
        ESP_LOGI("BluFiGuide", "3. 手机发送WiFi凭据");
        ESP_LOGI("BluFiGuide", "4. 设备尝试连接WiFi");
        ESP_LOGI("BluFiGuide", "5. 设备报告连接状态");
        ESP_LOGI("BluFiGuide", "6. 配置完成，断开BLE");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "✅ 配置成功后:");
        ESP_LOGI("BluFiGuide", "- WiFi信息自动保存");
        ESP_LOGI("BluFiGuide", "- 下次启动自动连接");
        ESP_LOGI("BluFiGuide", "- 可通过触摸手势重新配置");
        ESP_LOGI("BluFiGuide", "");
    }
    
    static void ShowTroubleshooting() {
        ESP_LOGI("BluFiGuide", "=== BluFi故障排除 ===");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "❌ 常见问题:");
        ESP_LOGI("BluFiGuide", "1. 无法发现设备");
        ESP_LOGI("BluFiGuide", "   - 确认BLE已启用");
        ESP_LOGI("BluFiGuide", "   - 检查设备距离");
        ESP_LOGI("BluFiGuide", "   - 重启BluFi广播");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "2. 连接失败");
        ESP_LOGI("BluFiGuide", "   - 检查WiFi密码");
        ESP_LOGI("BluFiGuide", "   - 确认WiFi信号强度");
        ESP_LOGI("BluFiGuide", "   - 检查WiFi频段(2.4GHz)");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "3. 配置不保存");
        ESP_LOGI("BluFiGuide", "   - 检查NVS分区");
        ESP_LOGI("BluFiGuide", "   - 确认写入权限");
        ESP_LOGI("BluFiGuide", "");
        ESP_LOGI("BluFiGuide", "🔧 解决方法:");
        ESP_LOGI("BluFiGuide", "- 重启设备");
        ESP_LOGI("BluFiGuide", "- 清除WiFi配置");
        ESP_LOGI("BluFiGuide", "- 使用官方EspBlufi App");
        ESP_LOGI("BluFiGuide", "");
    }
};
