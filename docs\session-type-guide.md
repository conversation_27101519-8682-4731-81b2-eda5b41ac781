# 会话类型区分功能使用指南

## 功能概述

本项目已实现了**会话类型区分**功能，可以区分不同触发方式的语音对话：
- **唤醒词触发** (`kSessionTypeWakeWord`) - 通过"你好小智"等唤醒词触发
- **手动触发** (`kSessionTypeManual`) - 通过长按按钮触发
- **触摸触发** (`kSessionTypeTouch`) - 预留的触摸屏触发方式

## 使用方法

### 1. 唤醒词触发对话
- **操作**: 说出"你好小智"等唤醒词
- **特点**: 自动检测语音结束，适合自然对话
- **会话类型**: `wake_word`
- **显示**: 正常的对话界面

### 2. 手动按钮触发对话
- **操作**: 长按Boot按钮
- **特点**: 精确控制录音时长，适合特定指令
- **会话类型**: `manual`
- **显示**: "🎤 手动识别中... 松开结束录音"

### 3. 服务器端区分处理
服务器可以根据 `session_type` 字段来区分不同的对话类型：

```json
{
  "session_id": "xxx",
  "type": "listen",
  "state": "start",
  "mode": "manual",
  "session_type": "wake_word"  // 或 "manual" 或 "touch"
}
```

## 技术实现

### 1. 协议层支持
- **文件**: `main/protocols/protocol.h` 和 `protocol.cc`
- **新增**: `SessionType` 枚举
- **修改**: `SendStartListening()` 方法支持会话类型参数

### 2. 应用层控制
- **文件**: `main/application.h` 和 `application.cc`
- **新增**: `StartManualListening()` 和 `StopManualListening()` 方法
- **修改**: `SetListeningMode()` 支持会话类型参数

### 3. 硬件层交互
- **文件**: `main/boards/waveshare-s3-touch-amoled-1.75/esp32-s3-touch-amoled-1.75.cc`
- **功能**: 按钮触发手动语音识别
- **反馈**: 不同的视觉提示

## 数据流程

### 唤醒词触发流程
```
用户说话 → 唤醒词检测 → WakeWordInvoke() → StartListening() 
→ SetListeningMode(mode, kSessionTypeWakeWord) → 服务器接收 session_type: "wake_word"
```

### 手动按钮触发流程
```
用户按下按钮 → OnPressDown() → StartManualListening() 
→ SetListeningMode(mode, kSessionTypeManual) → 服务器接收 session_type: "manual"
```

## 服务器端处理建议

### 1. 根据会话类型调整行为
```python
def handle_listen_start(data):
    session_type = data.get('session_type', 'wake_word')
    
    if session_type == 'wake_word':
        # 唤醒词触发：自然对话模式
        # - 更长的超时时间
        # - 更智能的语音结束检测
        # - 上下文相关的回复
        setup_natural_conversation_mode()
        
    elif session_type == 'manual':
        # 手动触发：指令模式
        # - 较短的超时时间
        # - 精确的指令识别
        # - 简洁的确认回复
        setup_command_mode()
        
    elif session_type == 'touch':
        # 触摸触发：快速交互模式
        setup_quick_interaction_mode()
```

### 2. 不同的ASR配置
```python
def configure_asr_by_session_type(session_type):
    if session_type == 'wake_word':
        return {
            'timeout': 10,  # 更长超时
            'language_model': 'conversational',
            'context_aware': True
        }
    elif session_type == 'manual':
        return {
            'timeout': 5,   # 较短超时
            'language_model': 'command',
            'context_aware': False
        }
```

## 优势

### 1. 用户体验优化
- **唤醒词模式**: 自然对话，适合日常交流
- **按钮模式**: 精确控制，适合特定指令
- **清晰区分**: 不同的视觉和听觉反馈

### 2. 服务器端优化
- **智能路由**: 根据触发方式选择不同的处理逻辑
- **资源优化**: 不同模式使用不同的ASR配置
- **上下文管理**: 更好的对话状态管理

### 3. 扩展性
- **易于扩展**: 可以轻松添加新的会话类型
- **向后兼容**: 默认为唤醒词类型，兼容旧版本
- **灵活配置**: 服务器端可以灵活处理不同类型

## 配置选项

### 1. 修改默认会话类型
在 `main/protocols/protocol.h` 中修改默认参数：
```cpp
virtual void SendStartListening(ListeningMode mode, SessionType session_type = kSessionTypeWakeWord);
```

### 2. 添加新的会话类型
在 `SessionType` 枚举中添加新类型：
```cpp
enum SessionType {
    kSessionTypeWakeWord,
    kSessionTypeManual,
    kSessionTypeTouch,
    kSessionTypeVoiceCommand,  // 新增
    kSessionTypeScheduled     // 新增
};
```

## 调试信息

系统会输出详细的调试信息：

```
I (12345) BootButton: Press down - Starting manual voice recognition
I (12346) Application: Starting manual listening (button triggered)
I (12347) Protocol: Sending start listening with session_type: manual
I (12348) BootButton: Press up - Stopping manual voice recognition
I (12349) Application: Stopping manual listening
```

## 故障排除

### 问题1: 服务器无法区分会话类型
- 检查协议实现是否正确发送 `session_type` 字段
- 确认服务器端已更新以支持新字段

### 问题2: 按钮触发无响应
- 检查按钮回调是否正确设置
- 确认 `StartManualListening()` 方法被调用

### 问题3: 会话类型显示错误
- 检查 `SetListeningMode()` 调用时的参数
- 确认协议发送的JSON格式正确

## 相关文件

- `main/protocols/protocol.h/.cc` - 协议层会话类型支持
- `main/application.h/.cc` - 应用层手动触发控制
- `main/boards/waveshare-s3-touch-amoled-1.75/esp32-s3-touch-amoled-1.75.cc` - 硬件交互

---

现在您可以通过不同的触发方式来区分不同类型的语音对话，为用户提供更好的交互体验！
