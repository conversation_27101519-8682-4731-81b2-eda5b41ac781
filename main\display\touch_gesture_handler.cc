#include "touch_gesture_handler.h"
#include <cmath>
#include <esp_timer.h>

const char* TouchGestureHandler::TAG = "TouchGestureHandler";

TouchGestureHandler::TouchGestureHandler(lv_indev_t* indev)
    : indev_(indev)
    , is_touching_(false)
    , gesture_detected_(false)
    , swipe_threshold_(50)
    , long_press_time_(500)
    , detect_timer_(nullptr)
{
    // 初始化所有手势为启用状态
    for (int i = 0; i < static_cast<int>(TouchGesture::MAX); i++) {
        gestures_enabled_[i] = true;
    }
    
    // 创建触摸检测定时器
    esp_timer_create_args_t timer_args = {
        .callback = [](void* arg) {
            auto self = static_cast<TouchGestureHandler*>(arg);
            self->onTouchDetectTimerCallback();
        },
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "touch_gesture_detect_timer",
        .skip_unhandled_events = true,
    };
    ESP_ERROR_CHECK(esp_timer_create(&timer_args, &detect_timer_));
    ESP_LOGI(TAG, "Touch gesture handler initialized");
}

TouchGestureHandler::~TouchGestureHandler() {
    if (detect_timer_) {
        esp_timer_stop(detect_timer_);
        esp_timer_delete(detect_timer_);
    }
    ESP_LOGI(TAG, "Touch gesture handler destroyed");
}

void TouchGestureHandler::Enabled(bool enabled) {
    if (enabled && !enabled_) {
        ESP_ERROR_CHECK(esp_timer_start_periodic(detect_timer_, 500));
    } else if (!enabled && enabled_) {
        ESP_ERROR_CHECK(esp_timer_stop(detect_timer_));
    }
    enabled_ = enabled;
}

void TouchGestureHandler::SetGestureEnabled(TouchGesture gesture, bool enabled) {
    int index = static_cast<int>(gesture);
    if (index >= 0 && index < static_cast<int>(TouchGesture::MAX)) {
        gestures_enabled_[index] = enabled;
    }
}

bool TouchGestureHandler::IsGestureEnabled(TouchGesture gesture) const {
    int index = static_cast<int>(gesture);
    if (index >= 0 && index < static_cast<int>(TouchGesture::MAX)) {
        return gestures_enabled_[index];
    }
    return false;
}

void TouchGestureHandler::onTouchDetectTimerCallback() {
    lv_indev_t* indev = indev_;
    if (!indev) {
        return;
    }

    bool pressed = (lv_indev_get_state(indev) != LV_INDEV_STATE_RELEASED);
    if (!pressed && !is_touching_) {
        return;
    }

    lv_point_t point;
    lv_indev_get_point(indev, &point);
    
    TouchPoint touch_point = {
        .x = point.x,
        .y = point.y,
        .pressed = pressed,
        .timestamp = lv_tick_get()
    };
    
    ProcessTouchEvent(touch_point);
}

void TouchGestureHandler::ProcessTouchEvent(const TouchPoint& point) {
    if (point.pressed && !is_touching_) {
        // 触摸开始
        is_touching_ = true;
        gesture_detected_ = false;
        touch_start_ = point;
        touch_current_ = point;
        
        ESP_LOGD(TAG, "Touch start: (%ld, %ld)", point.x, point.y);

    } else if (point.pressed && is_touching_) {
        // 触摸移动
        touch_current_ = point;

        // 检测滑动手势 & 长按手势
        if (!gesture_detected_) {
            TouchGesture gesture = DetectGesture(false);
            if (gesture != TouchGesture::NONE) {
                OnTouchGestureDispatch(gesture, touch_start_, touch_current_);
            }
        }

    } else if (!point.pressed && is_touching_) {
        // 触摸结束
        is_touching_ = false;
        touch_current_ = point;
        
        // 检测滑动手势 & 长按手势 & 点击
        if (!gesture_detected_) {
            TouchGesture gesture = DetectGesture(true);
            if (gesture != TouchGesture::NONE) {
                OnTouchGestureDispatch(gesture, touch_start_, touch_current_);
            }
        }
        
        ESP_LOGD(TAG, "Touch end: (%ld, %ld)", point.x, point.y);
    }
}

void TouchGestureHandler::OnTouchGestureDispatch(TouchGesture gesture, const TouchPoint& start, const TouchPoint& end) {
    float distance = CalculateDistance(start, end);
    ESP_LOGI(TAG, "Touch gesture: %s from (%ld,%ld) to (%ld,%ld), distance=%.2f",
            GetGestureName(gesture), start.x, start.y, end.x, end.y, distance);

    switch (gesture) {
        case TouchGesture::SWIPE_LEFT: {
            // 向左滑动
            if (on_swipe_left_) {
                on_swipe_left_();
            }
            break;
        }

        case TouchGesture::SWIPE_RIGHT: {
            // 向右滑动
            if (on_swipe_right_) {
                on_swipe_right_();
            }
            break;
        }

        case TouchGesture::SWIPE_UP: {
            // 向上滑动
            if (on_swipe_up_) {
                on_swipe_up_();
            }
            break;
        }

        case TouchGesture::SWIPE_DOWN: {
            // 向下滑动
            if (on_swipe_down_) {
                on_swipe_down_();
            }
            break;
        }

        case TouchGesture::TAP: {
            // 点击
            if (on_tap_) {
                on_tap_();
            }
            break;
        }

        case TouchGesture::LONG_PRESS: {
            // 长按
            if (on_long_press_) {
                on_long_press_();
            }
            break;
        }

        default:
            break;
    }
}

void TouchGestureHandler::OnSwipeLeft(std::function<void()> callback) {
    on_swipe_left_ = callback;
}

void TouchGestureHandler::OnSwipeRight(std::function<void()> callback) {
    on_swipe_right_ = callback;
}

void TouchGestureHandler::OnSwipeUp(std::function<void()> callback) {
    on_swipe_up_ = callback;
}

void TouchGestureHandler::OnSwipeDown(std::function<void()> callback) {
    on_swipe_down_ = callback;
}

void TouchGestureHandler::OnTap(std::function<void()> callback) {
    on_tap_ = callback;
}

void TouchGestureHandler::OnLongPress(std::function<void()> callback) {
    on_long_press_ = callback;
}

TouchGesture TouchGestureHandler::DetectGesture(bool ended) {
    // 检测滑动手势
    float distance = CalculateDistance(touch_start_, touch_current_);
    if (distance > swipe_threshold_) {
        TouchGesture gesture = DetectSwipeGesture(touch_start_, touch_current_);
        if (gesture != TouchGesture::NONE && IsGestureEnabled(gesture)) {
            gesture_detected_ = true;
            return gesture;
        }
    }

    // 检测长按手势
    uint32_t touch_duration = lv_tick_elaps(touch_start_.timestamp);
    if (IsGestureEnabled(TouchGesture::LONG_PRESS)) {
        if (is_touching_ && touch_duration >= long_press_time_) {
            gesture_detected_ = true;
            return TouchGesture::LONG_PRESS;
        }
    }

    // 检测点击手势
    if (ended) {
        if (distance < swipe_threshold_ && touch_duration < long_press_time_ && 
            IsGestureEnabled(TouchGesture::TAP)) {
            return TouchGesture::TAP;
        }
    }
    
    return TouchGesture::NONE;
}

TouchGesture TouchGestureHandler::DetectSwipeGesture(const TouchPoint& start_point, const TouchPoint& end_point) {
    int16_t dx = end_point.x - start_point.x;
    int16_t dy = end_point.y - start_point.y;
    
    // 计算主要方向
    if (abs(dx) > abs(dy)) {
        // 水平滑动
        if (dx > 0) {
            return TouchGesture::SWIPE_RIGHT;
        } else {
            return TouchGesture::SWIPE_LEFT;
        }
    } else {
        // 垂直滑动
        if (dy > 0) {
            return TouchGesture::SWIPE_DOWN;
        } else {
            return TouchGesture::SWIPE_UP;
        }
    }
}

float TouchGestureHandler::CalculateDistance(const TouchPoint& p1, const TouchPoint& p2) {
    int16_t dx = p2.x - p1.x;
    int16_t dy = p2.y - p1.y;
    return sqrt(dx * dx + dy * dy);
}

// 全局函数实现
const char* GetGestureName(TouchGesture gesture) {
    switch (gesture) {
        case TouchGesture::NONE: return "NONE";
        case TouchGesture::SWIPE_LEFT: return "SWIPE_LEFT";
        case TouchGesture::SWIPE_RIGHT: return "SWIPE_RIGHT";
        case TouchGesture::SWIPE_UP: return "SWIPE_UP";
        case TouchGesture::SWIPE_DOWN: return "SWIPE_DOWN";
        case TouchGesture::TAP: return "TAP";
        case TouchGesture::LONG_PRESS: return "LONG_PRESS";
        case TouchGesture::PINCH_IN: return "PINCH_IN";
        case TouchGesture::PINCH_OUT: return "PINCH_OUT";
        default: return "UNKNOWN";
    }
}

void CreateSwipeAnimation(lv_obj_t* obj, TouchGesture direction, int16_t distance, uint32_t duration) {
    if (!obj) return;
    
    lv_anim_t anim;
    lv_anim_init(&anim);
    lv_anim_set_var(&anim, obj);
    lv_anim_set_time(&anim, duration);
    lv_anim_set_path_cb(&anim, lv_anim_path_ease_out);
    
    // 获取当前位置
    lv_coord_t current_x = lv_obj_get_x(obj);
    lv_coord_t current_y = lv_obj_get_y(obj);
    
    switch (direction) {
        case TouchGesture::SWIPE_LEFT:
            lv_anim_set_exec_cb(&anim, (lv_anim_exec_xcb_t)lv_obj_set_x);
            lv_anim_set_values(&anim, current_x, current_x - distance);
            break;
        case TouchGesture::SWIPE_RIGHT:
            lv_anim_set_exec_cb(&anim, (lv_anim_exec_xcb_t)lv_obj_set_x);
            lv_anim_set_values(&anim, current_x, current_x + distance);
            break;
        case TouchGesture::SWIPE_UP:
            lv_anim_set_exec_cb(&anim, (lv_anim_exec_xcb_t)lv_obj_set_y);
            lv_anim_set_values(&anim, current_y, current_y - distance);
            break;
        case TouchGesture::SWIPE_DOWN:
            lv_anim_set_exec_cb(&anim, (lv_anim_exec_xcb_t)lv_obj_set_y);
            lv_anim_set_values(&anim, current_y, current_y + distance);
            break;
        default:
            return;
    }
    
    lv_anim_start(&anim);
}

void CreateElasticSwipeEffect(lv_obj_t* obj, TouchGesture direction, int16_t distance) {
    if (!obj) return;
    
    // 创建弹性效果：先移动到目标位置，然后弹回
    lv_anim_t anim1, anim2;
    
    // 第一阶段：移动到目标位置
    lv_anim_init(&anim1);
    lv_anim_set_var(&anim1, obj);
    lv_anim_set_time(&anim1, 200);
    lv_anim_set_path_cb(&anim1, lv_anim_path_ease_out);
    
    // 第二阶段：弹回原位置
    lv_anim_init(&anim2);
    lv_anim_set_var(&anim2, obj);
    lv_anim_set_time(&anim2, 300);
    lv_anim_set_delay(&anim2, 200);
    lv_anim_set_path_cb(&anim2, lv_anim_path_bounce);
    
    // 获取当前位置
    lv_coord_t current_x = lv_obj_get_x(obj);
    lv_coord_t current_y = lv_obj_get_y(obj);
    
    switch (direction) {
        case TouchGesture::SWIPE_LEFT:
        case TouchGesture::SWIPE_RIGHT: {
            int16_t target_x = (direction == TouchGesture::SWIPE_LEFT) ? 
                              current_x - distance : current_x + distance;
            
            lv_anim_set_exec_cb(&anim1, (lv_anim_exec_xcb_t)lv_obj_set_x);
            lv_anim_set_values(&anim1, current_x, target_x);
            
            lv_anim_set_exec_cb(&anim2, (lv_anim_exec_xcb_t)lv_obj_set_x);
            lv_anim_set_values(&anim2, target_x, current_x);
            break;
        }
        case TouchGesture::SWIPE_UP:
        case TouchGesture::SWIPE_DOWN: {
            int16_t target_y = (direction == TouchGesture::SWIPE_UP) ? 
                              current_y - distance : current_y + distance;
            
            lv_anim_set_exec_cb(&anim1, (lv_anim_exec_xcb_t)lv_obj_set_y);
            lv_anim_set_values(&anim1, current_y, target_y);
            
            lv_anim_set_exec_cb(&anim2, (lv_anim_exec_xcb_t)lv_obj_set_y);
            lv_anim_set_values(&anim2, target_y, current_y);
            break;
        }
        default:
            return;
    }
    
    lv_anim_start(&anim1);
    lv_anim_start(&anim2);
}
