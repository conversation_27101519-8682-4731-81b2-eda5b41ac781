#ifndef APP_MANAGER_H
#define APP_MANAGER_H

#include <lvgl.h>
#include <vector>
#include <functional>
#include <memory>
#include <esp_timer.h>
#include <esp_system.h>
#include <esp_log.h>

/**
 * @brief App状态枚举 (借鉴esp-brookesia)
 */
enum class AppState {
    UNINSTALLED = 0,    // 未安装
    INSTALLED,          // 已安装
    RUNNING,            // 运行中
    PAUSED,             // 暂停
    STOPPED             // 已停止
};

/**
 * @brief App类型枚举
 */
enum class AppType {
    MAIN_CHAT = 0,      // 主聊天App
    SETTINGS,           // 设置App
    SYSTEM_INFO,        // 系统信息App
    MEDIA_PLAYER,       // 媒体播放器App
    FILE_BROWSER,       // 文件浏览器App
    MAX_APPS
};

/**
 * @brief App切换动画类型
 */
enum class AppTransition {
    SLIDE_LEFT,         // 向左滑动
    SLIDE_RIGHT,        // 向右滑动
    FADE,               // 淡入淡出
    NONE                // 无动画
};

/**
 * @brief App事件类型 (借鉴esp-brookesia)
 */
enum class AppEvent {
    INSTALL,            // 安装事件
    UNINSTALL,          // 卸载事件
    START,              // 启动事件
    STOP,               // 停止事件
    PAUSE,              // 暂停事件
    RESUME,             // 恢复事件
    GESTURE_SWIPE_LEFT, // 左滑手势
    GESTURE_SWIPE_RIGHT,// 右滑手势
    GESTURE_SWIPE_UP,   // 上滑手势
    GESTURE_SWIPE_DOWN, // 下滑手势
    GESTURE_TAP,        // 点击手势
    GESTURE_LONG_PRESS  // 长按手势
};

class AppManager;

/**
 * @brief 抽象App基类 (借鉴esp-brookesia的App概念)
 */
class BaseApp {
public:
    BaseApp(AppType type, const char* name, const char* icon = nullptr);
    virtual ~BaseApp();

    friend AppManager;

    // App生命周期方法 (类似esp-brookesia)
    // 返回true, 表示app处理, 不再需要appmanager处理
    // 返回false, 则表示让AppManager处理默认逻辑
    virtual bool OnInstall() = 0;            // 安装App
    virtual bool OnUninstall() = 0;          // 卸载App
    virtual bool OnStart() = 0;              // 启动App
    virtual bool OnStop() = 0;               // 停止App
    virtual bool OnPause() { return true; }  // 暂停App (可选)
    virtual bool OnResume() { return true; } // 恢复App (可选)
    virtual void OnUpdate() {}               // 更新App内容 (可选)

    // 事件处理 (类似esp-brookesia的事件系统)
    virtual bool OnEvent(AppEvent event, void* data = nullptr);

    // App信息获取
    AppType GetType() const { return type_; }
    const char* GetName() const { return name_; }
    const char* GetIcon() const { return icon_; }
    AppState GetState() const { return state_; }
    lv_obj_t* GetContainer() const { return container_; }
    bool IsVisible() const { return is_visible_; }
    int GetId() const { return id_; }
    void SetId(int id) { id_ = id; }
    bool IsInstalled() const { return id_ != -1; };
    void SetVisible(bool is_visible) { is_visible_=is_visible; }
    void SetParent(lv_obj_t* parent) { parent_ = parent ?: lv_screen_active(); }

protected:
    AppType type_;
    const char* name_;
    const char* icon_;
    AppState state_;
    lv_obj_t* container_;
    bool is_visible_;
    uint64_t install_time_;
    uint64_t start_time_;
    /**
     * @brief App的唯一标识符, 也即appManager的数组apps_的下标
     */
    int id_ = -1;
    lv_obj_t* parent_;

    // 状态管理
    void SetState(AppState new_state);

    static const char* TAG;
};

/**
 * @brief App管理器 (借鉴esp-brookesia的AppManager)
 * 
 * 负责管理多个App的安装、启动、切换、生命周期等功能
 */
class AppManager {
public:
    AppManager(lv_obj_t* parent);
    ~AppManager();

    // App管理 (类似esp-brookesia)
    bool InstallApp(std::unique_ptr<BaseApp> app);
    bool UninstallApp(BaseApp& app);
    bool StartApp(int target_index, AppTransition transition = AppTransition::SLIDE_LEFT);
    bool StopApp(AppType type);
    bool PauseApp(AppType type);
    bool ResumeApp(AppType type);

    // 导航控制 (类似esp-brookesia的导航)
    bool NavigateToNext(AppTransition transition = AppTransition::SLIDE_LEFT);
    bool NavigateToPrevious(AppTransition transition = AppTransition::SLIDE_RIGHT);
    bool NavigateToApp(AppType type, AppTransition transition = AppTransition::SLIDE_LEFT);

    // 手势处理 (类似esp-brookesia的手势系统)
    void HandleGesture(AppEvent gesture_event);

    // 获取当前App
    BaseApp* GetCurrentApp() const;
    BaseApp* GetApp(AppType type) const;
    BaseApp* GetApp(int id) const;
    AppType GetCurrentAppType() const;
    
    // 设置回调
    void SetOnAppChanged(std::function<void(AppType, AppType)> callback);

    // 更新当前App
    void Update();

    // 获取已安装的App列表
    std::vector<AppType> GetInstalledApps() const;

private:
    lv_obj_t* parent_;
    std::vector<std::unique_ptr<BaseApp>> apps_;
    int current_app_index_;
    bool is_transitioning_;
    
    std::function<void(AppType, AppType)> on_app_changed_;

    // 内部方法
    void CreateAppTransition(BaseApp* from_app, BaseApp* to_app, AppTransition transition);
    void OnTransitionComplete(BaseApp* from_app, BaseApp* to_app);
    int FindAppIndex(AppType type) const;
    bool IsAppInstalled(const BaseApp& app) const {return app.IsInstalled(); }

    static const char* TAG;
};

/**
 * @brief 设置App
 */
class SettingsApp : public BaseApp {
public:
    SettingsApp();
    
    bool OnInstall() override;
    bool OnUninstall() override;
    bool OnStart() override;
    bool OnStop() override;
    bool OnEvent(AppEvent event, void* data = nullptr) override;

private:
    lv_obj_t* settings_list_;
    void CreateSettingsItems();
    
    static const char* TAG;
};

/**
 * @brief 系统信息App
 */
class SystemInfoApp : public BaseApp {
public:
    SystemInfoApp();
    
    bool OnInstall() override;
    bool OnUninstall() override;
    bool OnStart() override;
    bool OnStop() override;
    void OnUpdate() override;
    bool OnEvent(AppEvent event, void* data = nullptr) override;

private:
    lv_obj_t* info_list_;
    void UpdateSystemInfo();
    
    static const char* TAG;
};

/**
 * @brief 获取App类型名称
 */
const char* GetAppTypeName(AppType type);

/**
 * @brief 获取App状态名称
 */
const char* GetAppStateName(AppState state);

/**
 * @brief 获取App事件名称
 */
const char* GetAppEventName(AppEvent event);

#endif // APP_MANAGER_H
