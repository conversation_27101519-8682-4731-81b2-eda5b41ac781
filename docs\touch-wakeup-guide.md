# 触摸唤醒功能配置指南

## 功能概述

为 Waveshare ESP32-S3 Touch AMOLED 1.75" 开发板添加了**Boot按钮 + 触摸双重唤醒**功能，让设备可以通过两种方式从深度睡眠中唤醒。

## 实现方案

### 当前配置

由于该开发板的触摸中断引脚 (`TOUCH_INT_GPIO`) 未连接，我们使用以下方案：

```cpp
// 配置文件中的定义
#define BOOT_BUTTON_GPIO    GPIO_NUM_0   // Boot按钮
#define TOUCH_RST_GPIO      GPIO_NUM_40  // 触摸复位引脚
#define TOUCH_WAKEUP_GPIO   GPIO_NUM_40  // 使用触摸复位引脚作为唤醒源
```

### 唤醒源配置

```cpp
void ConfigureWakeupSource() {
    // 配置多个GPIO唤醒源
    uint64_t ext_wakeup_pin_mask = 0;
    
    // 1. Boot按钮 (GPIO0) - 低电平触发
    ext_wakeup_pin_mask |= (1ULL << BOOT_BUTTON_GPIO);
    
    // 2. 触摸唤醒引脚 (GPIO40) - 低电平触发
    ext_wakeup_pin_mask |= (1ULL << TOUCH_WAKEUP_GPIO);
    
    // 配置EXT1唤醒 - 任意一个引脚低电平触发
    esp_sleep_enable_ext1_wakeup(ext_wakeup_pin_mask, ESP_EXT1_WAKEUP_ANY_LOW);
}
```

## 使用方法

### 1. Boot按钮唤醒
- **操作**: 按下Boot按钮（GPIO0）
- **触发**: 低电平触发
- **显示**: "🔘 Boot按钮唤醒"

### 2. 触摸唤醒
- **操作**: 触摸屏幕（如果硬件支持）
- **触发**: 通过GPIO40检测
- **显示**: "👆 触摸唤醒"

## 硬件要求

### 理想配置（需要硬件修改）

如果要实现真正的触摸唤醒，需要：

1. **连接触摸中断引脚**：
   ```
   CST820触摸控制器的INT引脚 → ESP32-S3的某个GPIO
   ```

2. **修改配置**：
   ```cpp
   #define TOUCH_INT_GPIO  GPIO_NUM_XX  // 替换为实际连接的GPIO
   #define TOUCH_WAKEUP_GPIO GPIO_NUM_XX
   ```

### 当前配置（软件方案）

由于硬件限制，当前使用GPIO40作为替代方案：

1. **GPIO40功能**：
   - 原本是触摸复位引脚
   - 现在同时用作唤醒检测
   - 可以检测到某些触摸相关的信号变化

2. **工作原理**：
   - 当触摸屏被激活时，GPIO40可能会有电平变化
   - 通过监测这个变化来实现"伪触摸唤醒"

## 唤醒检测

### 唤醒原因识别

系统会自动检测并显示唤醒原因：

```cpp
void CheckWakeupReason() {
    esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();
    
    if (wakeup_reason == ESP_SLEEP_WAKEUP_EXT1) {
        uint64_t wakeup_pin_mask = esp_sleep_get_ext1_wakeup_status();
        
        if (wakeup_pin_mask & (1ULL << BOOT_BUTTON_GPIO)) {
            // Boot按钮唤醒
            display->SetChatMessage("system", "🔘 Boot按钮唤醒");
        }
        
        if (wakeup_pin_mask & (1ULL << TOUCH_WAKEUP_GPIO)) {
            // 触摸唤醒
            display->SetChatMessage("system", "👆 触摸唤醒");
        }
    }
}
```

### 调试信息

系统会输出详细的唤醒信息：

```
I (1234) TouchAMOLED-1.75: Wakeup caused by external signal using RTC_CNTL
I (1235) TouchAMOLED-1.75: Wakeup triggered by Boot button (GPIO0)
```

或

```
I (1234) TouchAMOLED-1.75: Wakeup caused by external signal using RTC_CNTL
I (1235) TouchAMOLED-1.75: Wakeup triggered by Touch (GPIO40)
```

## 配置选项

### 1. 修改唤醒引脚

如果您的硬件有不同的连接，可以修改配置：

```cpp
// 在 config.h 中修改
#define TOUCH_WAKEUP_GPIO GPIO_NUM_XX  // 替换为实际的GPIO
```

### 2. 调整唤醒触发方式

```cpp
// 当前：任意引脚低电平触发
esp_sleep_enable_ext1_wakeup(ext_wakeup_pin_mask, ESP_EXT1_WAKEUP_ANY_LOW);

// 可选：所有引脚都低电平才触发
esp_sleep_enable_ext1_wakeup(ext_wakeup_pin_mask, ESP_EXT1_WAKEUP_ALL_LOW);
```

### 3. 添加更多唤醒源

```cpp
// 添加第三个唤醒源
ext_wakeup_pin_mask |= (1ULL << GPIO_NUM_XX);
```

## 测试方法

### 1. 测试Boot按钮唤醒

1. 等待设备进入深度睡眠（默认300秒后）
2. 按下Boot按钮
3. 观察设备是否唤醒
4. 检查屏幕显示的唤醒原因

### 2. 测试触摸唤醒

1. 等待设备进入深度睡眠
2. 尝试触摸屏幕
3. 观察设备是否唤醒
4. 检查屏幕显示的唤醒原因

### 3. 查看调试信息

通过串口监控查看详细的唤醒信息：

```bash
# 使用ESP-IDF监控工具
idf.py monitor

# 或使用其他串口工具
# 波特率：115200
```

## 故障排除

### 问题1: 触摸唤醒不工作

**可能原因**：
- GPIO40没有连接到触摸相关信号
- 触摸控制器没有输出中断信号

**解决方案**：
1. 检查硬件连接
2. 尝试使用其他GPIO作为唤醒源
3. 考虑只使用Boot按钮唤醒

### 问题2: 设备无法进入睡眠

**可能原因**：
- GPIO状态不稳定
- 有其他任务阻止睡眠

**解决方案**：
1. 检查GPIO电平状态
2. 查看系统日志
3. 确认没有其他唤醒源干扰

### 问题3: 误唤醒

**可能原因**：
- GPIO噪声干扰
- 电平不稳定

**解决方案**：
1. 增加GPIO滤波
2. 调整上拉电阻配置
3. 增加唤醒延迟检测

## 硬件改进建议

### 方案1: 添加外部中断引脚

如果要实现真正的触摸唤醒，建议：

1. **硬件连接**：
   ```
   CST820的INT引脚 → ESP32-S3的GPIO21（或其他可用GPIO）
   ```

2. **软件配置**：
   ```cpp
   #define TOUCH_INT_GPIO  GPIO_NUM_21
   #define TOUCH_WAKEUP_GPIO GPIO_NUM_21
   ```

### 方案2: 使用电容触摸引脚

ESP32-S3内置电容触摸功能，可以使用：

```cpp
// 使用内置触摸引脚
#define TOUCH_WAKEUP_GPIO GPIO_NUM_1  // T1
// 或
#define TOUCH_WAKEUP_GPIO GPIO_NUM_2  // T2
```

## 总结

当前实现提供了Boot按钮和触摸双重唤醒功能的框架。虽然触摸唤醒可能受到硬件限制，但系统已经准备好支持真正的触摸中断引脚。

**优点**：
- ✅ Boot按钮唤醒完全可靠
- ✅ 支持多唤醒源框架
- ✅ 自动识别唤醒原因
- ✅ 易于扩展和修改

**限制**：
- ⚠️ 触摸唤醒依赖硬件连接
- ⚠️ 当前使用复位引脚作为替代方案

如需真正的触摸唤醒功能，建议进行硬件修改或选择支持触摸中断的开发板。
