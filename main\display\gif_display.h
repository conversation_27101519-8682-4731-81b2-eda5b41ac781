#pragma once

#include "lvgl.h"
#include <vector>
#include <string>
#include "display.h"
#include <esp_timer.h>

/**
 * @brief GIF动画显示类
 * 用于在LVGL中显示GIF动画
 */
class GifDisplay {
public:
    /**
     * @brief 构造函数
     * @param display 显示对象
     */
    GifDisplay(Display* display);
    
    /**
     * @brief 析构函数
     */
    ~GifDisplay();
    
    /**
     * @brief 加载GIF动画
     * @param frames 帧数组指针
     * @param frame_count 帧数
     * @param frame_duration 每帧持续时间(毫秒)
     * @return 是否成功
     */
    bool LoadGif(const lv_image_dsc_t** frames, int frame_count, int frame_duration = 100);
    
    /**
     * @brief 开始播放动画
     * @param loop 是否循环播放
     */
    void StartAnimation(bool loop = true);
    
    /**
     * @brief 停止播放动画
     */
    void StopAnimation();
    
    /**
     * @brief 暂停/恢复动画
     * @param pause true=暂停, false=恢复
     */
    void PauseAnimation(bool pause);
    
    /**
     * @brief 设置动画位置和大小
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     */
    void SetPosition(int x, int y, int width, int height);
    
    /**
     * @brief 获取显示对象
     * @return 显示对象
     */
    Display* GetObject() const { return img_obj_; }
    
    /**
     * @brief 检查是否正在播放
     * @return 是否正在播放
     */
    bool IsPlaying() const { return is_playing_; }

private:
    /**
     * @brief 定时器回调函数
     */
    void TimerCallback();
    
    /**
     * @brief 更新当前帧
     */
    void UpdateFrame();

private:
    Display* img_obj_;                    // LVGL图像对象
    esp_timer_handle_t timer_;                    // 动画定时器
    
    const lv_image_dsc_t** frames_;        // 帧数组
    int frame_count_;                      // 总帧数
    int current_frame_;                    // 当前帧索引
    int frame_duration_;                   // 每帧持续时间
    
    bool is_playing_;                      // 是否正在播放
    bool is_loop_;                         // 是否循环播放
    bool is_paused_;                       // 是否暂停
};

/**
 * @brief 便捷函数：创建简单的GIF动画
 * @param display 显示对象
 * @param frames 帧数组
 * @param frame_count 帧数
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @param loop 是否循环
 * @return GifDisplay对象指针
 */
GifDisplay* CreateGifAnimation(Display* display, 
                              const lv_image_dsc_t** frames, 
                              int frame_count,
                              int x = 0, int y = 0, 
                              int width = 100, int height = 100,
                              bool loop = true);
