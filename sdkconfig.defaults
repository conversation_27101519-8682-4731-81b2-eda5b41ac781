CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_CXX_EXCEPTIONS=y
CONFIG_COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE=1024

CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF=y
CONFIG_BOOTLOADER_LOG_LEVEL_NONE=y
CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS=y
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

CONFIG_HTTPD_MAX_REQ_HDR_LEN=2048
CONFIG_HTTPD_MAX_URI_LEN=2048

CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions/v1/16m.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000

CONFIG_ESP_TASK_WDT_TIMEOUT_S=10
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS=y

CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=n
CONFIG_ESP_WIFI_IRAM_OPT=n
CONFIG_ESP_WIFI_RX_IRAM_OPT=n
CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER=y

# These entries are copied from ESP-HI (ESP32C3) to reduce memory usage
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=6
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=8
CONFIG_NEWLIB_NANO_FORMAT=y
CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT=n

CONFIG_CODEC_I2C_BACKWARD_COMPATIBLE=n

# Fix ML307 FIFO Overflow
CONFIG_UART_ISR_IN_IRAM=y

# Fix ESP_SSL error
CONFIG_MBEDTLS_SSL_RENEGOTIATION=n

# LVGL 9.2.2

CONFIG_LV_OS_NONE=y
CONFIG_LV_USE_OS=0
CONFIG_LV_USE_CLIB_MALLOC=y
CONFIG_LV_USE_CLIB_STRING=y
CONFIG_LV_USE_CLIB_SPRINTF=y
CONFIG_LV_USE_IMGFONT=y

# Use compressed font
CONFIG_LV_FONT_FMT_TXT_LARGE=y
CONFIG_LV_USE_FONT_COMPRESSED=y
CONFIG_LV_USE_FONT_PLACEHOLDER=y

# Disable extra widgets to save flash size
CONFIG_LV_USE_ANIMIMG=n
CONFIG_LV_USE_CALENDAR=n
CONFIG_LV_USE_CALENDAR_HEADER_ARROW=n
CONFIG_LV_USE_CALENDAR_HEADER_DROPDOWN=n
CONFIG_LV_USE_CHART=n
CONFIG_LV_USE_KEYBOARD=n
CONFIG_LV_USE_LED=n
CONFIG_LV_USE_LIST=n
CONFIG_LV_USE_MENU=n
CONFIG_LV_USE_MSGBOX=n
CONFIG_LV_USE_SPAN=n
CONFIG_LV_USE_SPINBOX=n
CONFIG_LV_USE_SPINNER=n
CONFIG_LV_USE_TABVIEW=n
CONFIG_LV_USE_TILEVIEW=n
CONFIG_LV_USE_WIN=n

CONFIG_LV_BUILD_EXAMPLES=n

#
# for TikPal add config
#

# added for Blufi
#
# Bluetooth
#
CONFIG_BT_ENABLED=y
CONFIG_BT_BLUEDROID_ENABLED=y
CONFIG_BT_CONTROLLER_ENABLED=y

#
# Bluedroid Options
#
CONFIG_BT_BTC_TASK_STACK_SIZE=3072
CONFIG_BT_BLUEDROID_PINNED_TO_CORE_0=y
CONFIG_BT_BLUEDROID_PINNED_TO_CORE=0
CONFIG_BT_BTU_TASK_STACK_SIZE=4352
CONFIG_BT_BLUEDROID_ESP_COEX_VSC=y
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_GATTS_ENABLE=y
CONFIG_BT_BLE_BLUFI_ENABLE=y
CONFIG_BT_GATT_MAX_SR_PROFILES=8
CONFIG_BT_GATT_MAX_SR_ATTRIBUTES=100
CONFIG_BT_GATTS_SEND_SERVICE_CHANGE_AUTO=y
CONFIG_BT_GATTS_SEND_SERVICE_CHANGE_MODE=0

#
# BT DEBUG LOG LEVEL
#
CONFIG_BT_LOG_HCI_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_HCI_TRACE_LEVEL=2
CONFIG_BT_LOG_BTM_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_BTM_TRACE_LEVEL=2
CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_L2CAP_TRACE_LEVEL=2
CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL=2
CONFIG_BT_LOG_SDP_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_SDP_TRACE_LEVEL=2
CONFIG_BT_LOG_GAP_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_GAP_TRACE_LEVEL=2
CONFIG_BT_LOG_BNEP_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_BNEP_TRACE_LEVEL=2
CONFIG_BT_LOG_PAN_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_PAN_TRACE_LEVEL=2
CONFIG_BT_LOG_A2D_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_A2D_TRACE_LEVEL=2
CONFIG_BT_LOG_AVDT_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_AVDT_TRACE_LEVEL=2
CONFIG_BT_LOG_AVCT_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_AVCT_TRACE_LEVEL=2
CONFIG_BT_LOG_AVRC_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_AVRC_TRACE_LEVEL=2
CONFIG_BT_LOG_MCA_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_MCA_TRACE_LEVEL=2
CONFIG_BT_LOG_HID_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_HID_TRACE_LEVEL=2
CONFIG_BT_LOG_APPL_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_APPL_TRACE_LEVEL=2
CONFIG_BT_LOG_GATT_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_GATT_TRACE_LEVEL=2
CONFIG_BT_LOG_SMP_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_SMP_TRACE_LEVEL=2
CONFIG_BT_LOG_BTIF_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_BTIF_TRACE_LEVEL=2
CONFIG_BT_LOG_BTC_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_BTC_TRACE_LEVEL=2
CONFIG_BT_LOG_OSI_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_OSI_TRACE_LEVEL=2
CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_WARNING=y
CONFIG_BT_LOG_BLUFI_TRACE_LEVEL=2
# end of BT DEBUG LOG LEVEL

CONFIG_BT_ACL_CONNECTIONS=4
CONFIG_BT_MULTI_CONNECTION_ENBALE=y
CONFIG_BT_BLE_ESTAB_LINK_CONN_TOUT=30
CONFIG_BT_MAX_DEVICE_NAME_LEN=32
CONFIG_BT_BLE_RPA_TIMEOUT=900
CONFIG_BT_BLE_42_FEATURES_SUPPORTED=y
# end of Bluedroid Options

#
# Controller Options
#
CONFIG_BT_CTRL_MODE_EFF=1
CONFIG_BT_CTRL_BLE_MAX_ACT=6
CONFIG_BT_CTRL_BLE_MAX_ACT_EFF=6
CONFIG_BT_CTRL_BLE_STATIC_ACL_TX_BUF_NB=0
CONFIG_BT_CTRL_PINNED_TO_CORE_0=y
CONFIG_BT_CTRL_PINNED_TO_CORE=0
CONFIG_BT_CTRL_HCI_MODE_VHCI=y
CONFIG_BT_CTRL_HCI_TL=1
CONFIG_BT_CTRL_ADV_DUP_FILT_MAX=30
CONFIG_BT_BLE_CCA_MODE_NONE=y
CONFIG_BT_BLE_CCA_MODE=0
CONFIG_BT_CTRL_HW_CCA_VAL=20
CONFIG_BT_CTRL_HW_CCA_EFF=0
CONFIG_BT_CTRL_CE_LENGTH_TYPE_ORIG=y
CONFIG_BT_CTRL_CE_LENGTH_TYPE_EFF=0
CONFIG_BT_CTRL_TX_ANTENNA_INDEX_0=y
CONFIG_BT_CTRL_TX_ANTENNA_INDEX_EFF=0
CONFIG_BT_CTRL_RX_ANTENNA_INDEX_0=y
CONFIG_BT_CTRL_RX_ANTENNA_INDEX_EFF=0
CONFIG_BT_CTRL_DFT_TX_POWER_LEVEL_P9=y
CONFIG_BT_CTRL_DFT_TX_POWER_LEVEL_EFF=11
CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP=y
CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM=100
CONFIG_BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD=20
CONFIG_BT_CTRL_BLE_SCAN_DUPL=y
CONFIG_BT_CTRL_SCAN_DUPL_TYPE_DEVICE=y
CONFIG_BT_CTRL_SCAN_DUPL_TYPE=0
CONFIG_BT_CTRL_SCAN_DUPL_CACHE_SIZE=100
CONFIG_BT_CTRL_DUPL_SCAN_CACHE_REFRESH_PERIOD=0
CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_DIS=y
CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EFF=0

#
# MODEM SLEEP Options
#
CONFIG_BT_CTRL_SLEEP_MODE_EFF=0
CONFIG_BT_CTRL_SLEEP_CLOCK_EFF=0
CONFIG_BT_CTRL_HCI_TL_EFF=1
CONFIG_BT_CTRL_CHAN_ASS_EN=y
CONFIG_BT_CTRL_LE_PING_EN=y

#
# Common Options
#
CONFIG_BT_ALARM_MAX_NUM=50

# added for Blufi MBEDTLS
CONFIG_MBEDTLS_DHM_C=y

#
# Partition Table
#
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions/v1/16m_custom_wakeword.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions/v1/16m_custom_wakeword.csv"

CONFIG_BOARD_TYPE_BREAD_COMPACT_WIFI=n
CONFIG_BOARD_TYPE_ESP32S3_Touch_AMOLED_1_75=y

CONFIG_SR_MN_CN_MULTINET6_QUANT=y
CONFIG_SR_WN_WN9_NIHAOXIAOZHI_TTS=n

# use wechat
CONFIG_USE_WECHAT_MESSAGE_STYLE=y

# diy wake word
CONFIG_USE_AFE_WAKE_WORD=n
CONFIG_USE_CUSTOM_WAKE_WORD=y
CONFIG_CUSTOM_WAKE_WORD="ti ke pou"
CONFIG_CUSTOM_WAKE_WORD_DISPLAY="浣犲ソ鍒樻稕"
