#ifndef __AXP2101_H__
#define __AXP2101_H__

#include "i2c_device.h"

#define XPOWERS_AXP2101_STATUS1                          (0x00)
#define XPOWERS_AXP2101_STATUS2                          (0x01)
#define XPOWERS_AXP2101_IC_TYPE                          (0x03)

#define XPOWERS_AXP2101_COMMON_CONFIG                    (0x10)

#define XPOWERS_AXP2101_PWROFF_EN                        (0x22)
#define XPOWERS_AXP2101_IRQ_OFF_ON_LEVEL_CTRL            (0x27)

#define XPOWERS_AXP2101_ADC_CHANNEL_CTRL                 (0x30)
#define XPOWERS_AXP2101_ADC_DATA_RESULT0                 (0x34)
#define XPOWERS_AXP2101_ADC_DATA_RESULT1                 (0x35)
#define XPOWERS_AXP2101_ADC_DATA_RESULT4                 (0x38)
#define XPOWERS_AXP2101_ADC_DATA_RESULT5                 (0x39)
#define XPOWERS_AXP2101_ADC_DATA_RESULT6                 (0x3A)
#define XPOWERS_AXP2101_ADC_DATA_RESULT7                 (0x3B)
#define XPOWERS_AXP2101_ADC_DATA_RESULT8                 (0x3C)
#define XPOWERS_AXP2101_ADC_DATA_RESULT9                 (0x3D)

#define XPOWERS_AXP2101_IPRECHG_SET                      (0x61)
#define XPOWERS_AXP2101_ICC_CHG_SET                      (0x62)
#define XPOWERS_AXP2101_ITERM_CHG_SET_CTRL               (0x63)
#define XPOWERS_AXP2101_CV_CHG_VOL_SET                   (0x64)

#define XPOWERS_AXP2101_DC_ONOFF_DVM_CTRL                (0x80)
#define XPOWERS_AXP2101_DC_VOL0_CTRL                     (0x82)

#define XPOWERS_AXP2101_LDO_ONOFF_CTRL0                  (0x90)
#define XPOWERS_AXP2101_LDO_ONOFF_CTRL1                  (0x91)
#define XPOWERS_AXP2101_LDO_VOL0_CTRL                    (0x92)

#define XPOWERS_AXP2101_BAT_PERCENT_DATA                 (0xA4)

//XPOWERS INTERRUPT STATUS REGISTER
//与es7210共享reg????
#define XPOWERS_AXP2101_INTSTS1                          (0x48)
#define XPOWERS_AXP2101_INTSTS2                          (0x49)
#define XPOWERS_AXP2101_INTSTS3                          (0x4A)
#define XPOWERS_AXP2101_INTSTS_CNT                       (3)

//XPOWERS INTERRUPT REGISTER
#define XPOWERS_AXP2101_INTEN1                           (0x40)
#define XPOWERS_AXP2101_INTEN2                           (0x41)
#define XPOWERS_AXP2101_INTEN3                           (0x42)

#define _BV(b)                          (1ULL << (uint64_t)(b))
#define IS_BIT_SET(val,mask)            (((val)&(mask)) == (mask))

#define XPOWERS_AXP2101_CONVERSION(raw)                 (22.0 + (7274 - raw) / 20.0)

typedef enum {
    XPOWERS_AXP2101_CHG_TRI_STATE,   //tri_charge
    XPOWERS_AXP2101_CHG_PRE_STATE,   //pre_charge
    XPOWERS_AXP2101_CHG_CC_STATE,    //constant charge
    XPOWERS_AXP2101_CHG_CV_STATE,    //constant voltage
    XPOWERS_AXP2101_CHG_DONE_STATE,  //charge done
    XPOWERS_AXP2101_CHG_STOP_STATE,  //not chargin
} xpowers_chg_status_t;

/**
 * @brief axp2101 charge target voltage parameters.
 */
typedef enum __xpowers_axp2101_chg_vol {
    XPOWERS_AXP2101_CHG_VOL_4V = 1,
    XPOWERS_AXP2101_CHG_VOL_4V1,
    XPOWERS_AXP2101_CHG_VOL_4V2,
    XPOWERS_AXP2101_CHG_VOL_4V35,
    XPOWERS_AXP2101_CHG_VOL_4V4,
    XPOWERS_AXP2101_CHG_VOL_MAX
} xpowers_axp2101_chg_vol_t;

/**
 * @brief axp2101 charge currnet voltage parameters.
 */
typedef enum __xpowers_axp2101_chg_curr {
    XPOWERS_AXP2101_CHG_CUR_0MA,
    XPOWERS_AXP2101_CHG_CUR_100MA = 4,
    XPOWERS_AXP2101_CHG_CUR_125MA,
    XPOWERS_AXP2101_CHG_CUR_150MA,
    XPOWERS_AXP2101_CHG_CUR_175MA,
    XPOWERS_AXP2101_CHG_CUR_200MA,
    XPOWERS_AXP2101_CHG_CUR_300MA,
    XPOWERS_AXP2101_CHG_CUR_400MA,
    XPOWERS_AXP2101_CHG_CUR_500MA,
    XPOWERS_AXP2101_CHG_CUR_600MA,
    XPOWERS_AXP2101_CHG_CUR_700MA,
    XPOWERS_AXP2101_CHG_CUR_800MA,
    XPOWERS_AXP2101_CHG_CUR_900MA,
    XPOWERS_AXP2101_CHG_CUR_1000MA,
} xpowers_axp2101_chg_curr_t;

typedef enum {
    XPOWERS_AXP2101_PRECHARGE_0MA,
    XPOWERS_AXP2101_PRECHARGE_25MA,
    XPOWERS_AXP2101_PRECHARGE_50MA,
    XPOWERS_AXP2101_PRECHARGE_75MA,
    XPOWERS_AXP2101_PRECHARGE_100MA,
    XPOWERS_AXP2101_PRECHARGE_125MA,
    XPOWERS_AXP2101_PRECHARGE_150MA,
    XPOWERS_AXP2101_PRECHARGE_175MA,
    XPOWERS_AXP2101_PRECHARGE_200MA,
} xpowers_prechg_t;

typedef enum {
    XPOWERS_AXP2101_CHG_ITERM_0MA,
    XPOWERS_AXP2101_CHG_ITERM_25MA,
    XPOWERS_AXP2101_CHG_ITERM_50MA,
    XPOWERS_AXP2101_CHG_ITERM_75MA,
    XPOWERS_AXP2101_CHG_ITERM_100MA,
    XPOWERS_AXP2101_CHG_ITERM_125MA,
    XPOWERS_AXP2101_CHG_ITERM_150MA,
    XPOWERS_AXP2101_CHG_ITERM_175MA,
    XPOWERS_AXP2101_CHG_ITERM_200MA,
} xpowers_axp2101_chg_iterm_t;

/**
 * @brief axp2101 interrupt control mask parameters.
 */
typedef enum __xpowers_axp2101_irq {
    //! IRQ1 REG 40H
    XPOWERS_AXP2101_BAT_NOR_UNDER_TEMP_IRQ   = _BV(0),   // Battery Under Temperature in Work
    XPOWERS_AXP2101_BAT_NOR_OVER_TEMP_IRQ    = _BV(1),   // Battery Over Temperature in Work mode
    XPOWERS_AXP2101_BAT_CHG_UNDER_TEMP_IRQ   = _BV(2),   // Battery Under Temperature in Charge mode IRQ(bcut_irq)
    XPOWERS_AXP2101_BAT_CHG_OVER_TEMP_IRQ    = _BV(3),   // Battery Over Temperature in Charge mode IRQ(bcot_irq) enable
    XPOWERS_AXP2101_GAUGE_NEW_SOC_IRQ        = _BV(4),   // Gauge New SOC IRQ(lowsoc_irq) enable ???
    XPOWERS_AXP2101_WDT_TIMEOUT_IRQ          = _BV(5),   // Gauge Watchdog Timeout IRQ(gwdt_irq) enable
    XPOWERS_AXP2101_WARNING_LEVEL1_IRQ       = _BV(6),   // SOC drop to Warning Level1 IRQ(socwl1_irq) enable
    XPOWERS_AXP2101_WARNING_LEVEL2_IRQ       = _BV(7),   // SOC drop to Warning Level2 IRQ(socwl2_irq) enable

    //! IRQ2 REG 41H
    XPOWERS_AXP2101_PKEY_POSITIVE_IRQ        = _BV(8),   // POWERON Positive Edge IRQ(ponpe_irq_en) enable
    XPOWERS_AXP2101_PKEY_NEGATIVE_IRQ        = _BV(9),   // POWERON Negative Edge IRQ(ponne_irq_en) enable
    XPOWERS_AXP2101_PKEY_LONG_IRQ            = _BV(10),  // POWERON Long PRESS IRQ(ponlp_irq) enable
    XPOWERS_AXP2101_PKEY_SHORT_IRQ           = _BV(11),  // POWERON Short PRESS IRQ(ponsp_irq_en) enable
    XPOWERS_AXP2101_BAT_REMOVE_IRQ           = _BV(12),  // Battery Remove IRQ(bremove_irq) enable
    XPOWERS_AXP2101_BAT_INSERT_IRQ           = _BV(13),  // Battery Insert IRQ(binsert_irq) enabl
    XPOWERS_AXP2101_VBUS_REMOVE_IRQ          = _BV(14),  // VBUS Remove IRQ(vremove_irq) enabl
    XPOWERS_AXP2101_VBUS_INSERT_IRQ          = _BV(15),  // VBUS Insert IRQ(vinsert_irq) enable

    //! IRQ3 REG 42H
    XPOWERS_AXP2101_BAT_OVER_VOL_IRQ         = _BV(16),  // Battery Over Voltage Protection IRQ(bovp_irq) enable
    XPOWERS_AXP2101_CHAGER_TIMER_IRQ         = _BV(17),  // Charger Safety Timer1/2 expire IRQ(chgte_irq) enable
    XPOWERS_AXP2101_DIE_OVER_TEMP_IRQ        = _BV(18),  // DIE Over Temperature level1 IRQ(dotl1_irq) enable
    XPOWERS_AXP2101_BAT_CHG_START_IRQ        = _BV(19),  // Charger start IRQ(chgst_irq) enable
    XPOWERS_AXP2101_BAT_CHG_DONE_IRQ         = _BV(20),  // Battery charge done IRQ(chgdn_irq) enable
    XPOWERS_AXP2101_BATFET_OVER_CURR_IRQ     = _BV(21),  // BATFET Over Current Protection IRQ(bocp_irq) enable
    XPOWERS_AXP2101_LDO_OVER_CURR_IRQ        = _BV(22),  // LDO Over Current IRQ(ldooc_irq) enable
    XPOWERS_AXP2101_WDT_EXPIRE_IRQ           = _BV(23),  // Watchdog Expire IRQ(wdexp_irq) enable

    XPOWERS_AXP2101_ALL_IRQ                  = (0xFFFFFFFFUL)
} xpowers_axp2101_irq_t;

class Axp2101 : public I2cDevice {
public:
    Axp2101(i2c_master_bus_handle_t i2c_bus, uint8_t addr);
    bool IsCharging();
    bool IsDischarging();
    bool IsChargingDone();
    int GetBatteryLevel();
    float GetBatteryVoltage();
    float GetTemperature();
    void PowerOff();
    float GetSystemVoltage();

    // 新增电源检测方法
    bool IsUsbConnected();
    bool IsUsbPowered();
    bool IsBatteryPresent();
    float GetUsbVoltage();
    float GetUsbCurrent();
    int GetPowerSource();  // 0=电池, 1=USB, 2=两者都有

    /**
    * @brief  Get the interrupt controller mask value.
    * @retval   Mask value corresponds to xpowers_axp2101_irq_t ,
    */
    uint64_t getIrqStatus(void)
    {
        statusRegister[0] = ReadReg(XPOWERS_AXP2101_INTSTS1);
        statusRegister[1] = ReadReg(XPOWERS_AXP2101_INTSTS2);
        statusRegister[2] = ReadReg(XPOWERS_AXP2101_INTSTS3);
        return (uint32_t)(statusRegister[0] << 16) | (uint32_t)(statusRegister[1] << 8) | (uint32_t)(statusRegister[2]);
    }

    /**
     * @brief  Clear interrupt controller state.
     */
    void clearIrqStatus()
    {
        for (int i = 0; i < XPOWERS_AXP2101_INTSTS_CNT; i++) {
            WriteReg(XPOWERS_AXP2101_INTSTS1 + i, 0xFF);
            statusRegister[i] = 0;
        }
    }

    bool isPekeyShortPressIrq(void)
    {
        uint8_t mask = XPOWERS_AXP2101_PKEY_SHORT_IRQ  >> 8;
        if (intRegister[1] & mask) {
            return IS_BIT_SET(statusRegister[1], mask);
        }
        return false;
    }

    bool isPekeyLongPressIrq(void)
    {
        uint8_t mask = XPOWERS_AXP2101_PKEY_LONG_IRQ  >> 8;
        if (intRegister[1] & mask) {
            return IS_BIT_SET(statusRegister[1], mask);
        }
        return false;
    }

    bool isPekeyNegativeIrq(void)
    {
        uint8_t mask = XPOWERS_AXP2101_PKEY_NEGATIVE_IRQ  >> 8;
        if (intRegister[1] & mask) {
            return IS_BIT_SET(statusRegister[1], mask);
        }
        return false;
    }

    bool isPekeyPositiveIrq(void)
    {
        uint8_t mask = XPOWERS_AXP2101_PKEY_POSITIVE_IRQ  >> 8;
        if (intRegister[1] & mask) {
            return IS_BIT_SET(statusRegister[1], mask);
        }
        return false;
    }


protected:
    void inline clrRegisterBit(uint8_t reg, uint8_t bit)
    {
        int val = ReadReg(reg);
        if (val == -1) {
            return;
        }
        WriteReg(reg, (val & (~_BV(bit))));
    }

    void inline setRegisterBit(uint8_t reg, uint8_t bit)
    {
        int val = ReadReg(reg);
        if (val == -1) {
            return;
        }
        WriteReg(reg, (val | (_BV(bit))));
    }

    /**
     * @brief  Eanble PMU interrupt control mask .
     * @param  opt: View the related chip type xpowers_axp2101_irq_t enumeration
     *              parameters in "XPowersParams.hpp"
     * @retval
     */
    bool enableIRQ(uint64_t opt)
    {
        return setInterruptImpl(opt, true);
    }

    /**
     * @brief  Disable PMU interrupt control mask .
     * @param  opt: View the related chip type xpowers_axp2101_irq_t enumeration
     *              parameters in "XPowersParams.hpp"
     * @retval
     */
    bool disableIRQ(uint64_t opt)
    {
        return setInterruptImpl(opt, false);
    }

private:
    int GetBatteryCurrentDirection();
    uint8_t statusRegister[XPOWERS_AXP2101_INTSTS_CNT];
    uint8_t intRegister[XPOWERS_AXP2101_INTSTS_CNT];

    /*
     * Interrupt control functions
     */
    bool setInterruptImpl(uint32_t opts, bool enable)
    {
        int res = 0;
        uint8_t data = 0, value = 0;
        //log_d("%s - HEX:0x%x \n", enable ? "ENABLE" : "DISABLE", opts);
        if (opts & 0x0000FF) {
            value = opts & 0xFF;
            // log_d("Write INT0: %x\n", value);
            data = ReadReg(XPOWERS_AXP2101_INTEN1);
            intRegister[0] =  enable ? (data | value) : (data & (~value));
            res |= WriteReg(XPOWERS_AXP2101_INTEN1, intRegister[0]);
        }
        if (opts & 0x00FF00) {
            value = opts >> 8;
            // log_d("Write INT1: %x\n", value);
            data = ReadReg(XPOWERS_AXP2101_INTEN2);
            intRegister[1] =  enable ? (data | value) : (data & (~value));
            res |= WriteReg(XPOWERS_AXP2101_INTEN2, intRegister[1]);
        }
        if (opts & 0xFF0000) {
            value = opts >> 16;
            // log_d("Write INT2: %x\n", value);
            data = ReadReg(XPOWERS_AXP2101_INTEN3);
            intRegister[2] =  enable ? (data | value) : (data & (~value));
            res |= WriteReg(XPOWERS_AXP2101_INTEN3, intRegister[2]);
        }
        return res == 0;
    }

    bool inline getRegisterBit(uint8_t reg, uint8_t bit)
    {
        int val = ReadReg(reg);
        if (val == -1) {
            return false;
        }
        return val & _BV(bit);
    }

    uint16_t inline readRegisterH6L8(uint8_t highReg, uint8_t lowReg)
    {
        int h6 = ReadReg(highReg);
        int l8 = ReadReg(lowReg);
        if (h6 == -1 || l8 == -1)return 0;
        return ((h6 & 0x3F) << 8) | l8;
    }

    uint16_t inline readRegisterH5L8(uint8_t highReg, uint8_t lowReg)
    {
        int h5 = ReadReg(highReg);
        int l8 = ReadReg(lowReg);
        if (h5 == -1 || l8 == -1)return 0;
        return ((h5 & 0x1F) << 8) | l8;
    }


    bool isVbusGood(void)
    {
        return  getRegisterBit(XPOWERS_AXP2101_STATUS1, 5);
    }

    /**
     * @brief Query whether the current USB is connected
     * @retval true to access,false to not access
     */
    bool isVbusIn(void)
    {
        return getRegisterBit(XPOWERS_AXP2101_STATUS2, 3) == 0 && isVbusGood();
    }

    /**
     * @brief Query whether the current battery is connected
     * @retval true to access,false to not access
     */
    // getBatPresentState
    bool isBatteryConnect(void)
    {
        return  getRegisterBit(XPOWERS_AXP2101_STATUS1, 3);
    }

    /**
    * @brief Query whether it is currently in charging state
    * @retval true to charge,false to not charge
    */
    bool isCharging(void)
    {
        return (ReadReg(XPOWERS_AXP2101_STATUS2) >> 5) == 0x01;
    }

    /**
     * @brief Query whether the current is in the discharge state
     * @retval true the battery is discharged,false is not discharged
     */
    bool isDischarge(void)
    {
        return (ReadReg(XPOWERS_AXP2101_STATUS2) >> 5) == 0x02;
    }

    /**
     * @brief 是否待机
     * @retval true 待机,false 否
     */
    bool isStandby(void)
    {
        return (ReadReg(XPOWERS_AXP2101_STATUS2) >> 5) == 0x00;
    }

    /**
     * @brief 电池温度
     * @retval 温度值
     */
    float getTemperature(void)
    {
        uint16_t raw = readRegisterH6L8(XPOWERS_AXP2101_ADC_DATA_RESULT8, XPOWERS_AXP2101_ADC_DATA_RESULT9);
        return XPOWERS_AXP2101_CONVERSION(raw);
    }

    /**
    * @brief  Get PMU SYS main Voltage
    * @retval Voltage unit: millivolt
    */
    uint16_t getSystemVoltage(void)
    {
        return readRegisterH6L8(XPOWERS_AXP2101_ADC_DATA_RESULT6, XPOWERS_AXP2101_ADC_DATA_RESULT7);
    }

    /**
    * @brief  Get PMU VBUS/USB Voltage
    * @retval Voltage unit: millivolt , 0 is no vbus is connected
    */
    uint16_t getVbusVoltage(void)
    {
        if (!isVbusIn()) {
            return 0;
        }
        return readRegisterH6L8(XPOWERS_AXP2101_ADC_DATA_RESULT4, XPOWERS_AXP2101_ADC_DATA_RESULT5);
    }

    /**
    * @brief  Get battery Voltage
    * @retval Voltage unit: millivolt , 0 is no battery is connected
    */
    uint16_t getBattVoltage(void)
    {
        if (!isBatteryConnect()) {
            return 0;
        }
        return readRegisterH5L8(XPOWERS_AXP2101_ADC_DATA_RESULT0, XPOWERS_AXP2101_ADC_DATA_RESULT1);
    }

    /**
    * @brief  Get battery percentage
    * @retval 0~100% , -1 no battery is connected
    */
    int getBatteryPercent(void)
    {
        if (!isBatteryConnect()) {
            return -1;
        }
        return ReadReg(XPOWERS_AXP2101_BAT_PERCENT_DATA);
    }

    /**
    * @brief  获取充电状态
    * @retval 充电状态
    */
    xpowers_chg_status_t getChargerStatus(void)
    {
        int val = ReadReg(XPOWERS_AXP2101_STATUS2);
        if (val == -1)return XPOWERS_AXP2101_CHG_STOP_STATE;
        val &= 0x07;
        return (xpowers_chg_status_t)val;
    }

    bool isPowerOn(void)
    {
        return getRegisterBit(XPOWERS_AXP2101_STATUS2, 4);
    }

    bool isPowerOff(void)
    {
        return getRegisterBit(XPOWERS_AXP2101_STATUS2, 4);
    }

    bool getBatfetState(void)
    {
        return  getRegisterBit(XPOWERS_AXP2101_STATUS1, 4);
    }

    bool isBatInActiveModeState(void)
    {
        return  getRegisterBit(XPOWERS_AXP2101_STATUS1, 2);
    }

    bool getThermalRegulationStatus(void)
    {
        return  getRegisterBit(XPOWERS_AXP2101_STATUS1, 1);
    }

    bool getCurrnetLimitStatus(void)
    {
        return getRegisterBit(XPOWERS_AXP2101_STATUS1, 0);
    }

};

#endif
