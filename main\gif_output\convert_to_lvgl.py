#!/usr/bin/env python3
# 自动生成的LVGL转换脚本

import sys
import os
sys.path.append('D:\\Data\\workspaces\\c\\esp-box-examples\\xiaozhi-esp32\\scripts\\Image_Converter')

from LVGLImage import LVGLImage, ColorFormat, OutputFormat

# 转换参数
frames = ['my_animation_frame_001.png', 'my_animation_frame_002.png', 'my_animation_frame_003.png', 'my_animation_frame_004.png', 'my_animation_frame_005.png', 'my_animation_frame_006.png', 'my_animation_frame_007.png', 'my_animation_frame_008.png', 'my_animation_frame_009.png', 'my_animation_frame_010.png', 'my_animation_frame_011.png']
var_name = "my_animation"

for i, frame_path in enumerate(frames):
    print(f"转换帧 {i+1}/{len(frames)}: {frame_path}")
    
    # 创建LVGL图像对象
    img = LVGLImage().from_png(frame_path, ColorFormat.RGB565)
    
    # 输出为C数组
    output_path = frame_path.replace('.png', '.c')
    img.to_c_array(output_path)
    
    print(f"生成: {output_path}")

print("转换完成！")
print("请将生成的.c文件添加到您的项目中。")
