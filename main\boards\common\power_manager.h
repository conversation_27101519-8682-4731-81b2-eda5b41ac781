#pragma once

#include "power_save_timer.h"
#include "pmic.h"
#include <esp_log.h>
#include <lvgl.h>
#include <functional>

// 电源类型枚举
enum class PowerSourceType {
    UNKNOWN = -1,
    BATTERY_ONLY = 0,
    USB_ONLY = 1,
    USB_AND_BATTERY = 2
};

/**
 * @brief 电源管理类
 */
class PowerManagement {
private:
    PowerSaveTimer* power_save_timer_;
    Pmic* pmic_;
    /**
     * @brief 电源监控定时器
     */
    esp_timer_handle_t monitor_timer_;
    /**
     * @brief 电源监控定时器周期，默认50s
     */
    uint32_t monitor_period_;
    /**
     * @brief 是否已启用
     */
    bool enabled_ = false;
    /**
     * @brief 是否在充电
     */
    bool is_charging_ = false;
    /**
     * @brief 温度过高是否需要报警，默认不报警
     */
    bool is_too_hot_warning_ = false;

    /**
     * @brief 电源监控定时器回调函数
     */
    void OnPowerMonitorTimer();

    /**
     * @brief 电源电压过低需要充电的电压
     */
    float voltage_to_charge_;
    /**
     * @brief 电源电量过低需要充电的电量
     */
    int battery_level_to_charge_;
    /**
     * @brief 电源电压过低需要关机的电压
     */
    float voltage_to_shutdown_;
    /**
     * @brief 电源电量过低需要关机的电量
     */
    int battery_level_to_shutdown_;
    /**
     * @brief 电源温度过高需要报警的温度
     */
    float temp_to_warning_;

    /**
     * @brief 充电开始的回调函数
     */
    std::function<void()> on_enter_charge_mode_;
    /**
     * @brief 充电结束的回调函数
     */
    std::function<void()> on_exit_charge_mode_;
    /**
     * @brief 电量过低需要充电的回调函数
     */
    std::function<void()> on_need_charge_;
    /**
     * @brief 温度过高需要报警的回调函数
     */
    std::function<void(float temperature)> on_enter_too_hot_;
    /**
     * @brief 温度正常撤销报警的回调函数
     */
    std::function<void()> on_exit_too_hot_;

    /**
     * @brief 计数器: 进入充电模式的次数
     */
    size_t cnt_enter_charge_ = 0;
    /**
     * @brief 计数器: 离开充电模式的次数
     */
    size_t cnt_leave_charge_ = 0;
    /**
     * @brief 计数器: 请求关机的次数
     */
    size_t cnt_shutdown_ = 0;
    /**
     * @brief 计数器: 温度过高需要报警的次数
     */
    size_t cnt_enter_too_hot_ = 0;
    /**
     * @brief 计数器: 温度正常撤销报警的次数
     */
    size_t cnt_exit_too_hot_ = 0;
    /**
     * @brief 计数器: 电量过低需要充电的次数
     */
    size_t cnt_need_charge_ = 0;

public:
    PowerManagement(PowerSaveTimer* timer
        , Pmic* pmic
        , uint32_t monitor_period = 50000000 // 50s
        , float voltage_to_charge = Pmic::BATTERY_LOW_VOLTAGE
        , int battery_level_to_charge = Pmic::BATTERY_LOW_LEVEL
        , float voltage_to_shutdown = Pmic::BATTERY_MIN_VOLTAGE
        , int battery_level_to_shutdown = Pmic::BATTERY_MIN_LEVEL
        , float temp_to_warning = 0
    );
    ~PowerManagement();

    /**
     * @brief 启用电源管理
     * @param enabled 是否启用: true=启用
     */
    void SetEnabled(bool enabled, uint32_t monitor_period = 50000000);

    /**
     * @brief 设置充电开始的回调函数
     */
    void OnEnterChargeMode(std::function<void()> callback);
    /**
     * @brief 设置充电结束的回调函数
     */
    void OnExitChargeMode(std::function<void()> callback);
    /**
     * @brief 设置电量过低需要充电的回调函数
     */
    void OnNeedCharge(std::function<void()> callback);
    /**
     * @brief 设置温度过高需要报警的回调函数
     */
    void OnEnterTooHot(std::function<void(float temperature)> callback);
    /**
     * @brief 设置温度正常撤销报警的回调函数
     */
    void OnExitTooHot(std::function<void()> callback);

    /**
     * @brief 获取电源电压过低需要充电的电压
     */
    float GetVoltageToCharge() const { return voltage_to_charge_; }
    /**
     * @brief 设置电源电压过低需要充电的电压
     */
    void SetVoltageToCharge(float voltage_to_charge);

    /**
     * @brief 获取电源电量过低需要充电的电量
     */
    int GetBatteryLevelToCharge() const { return battery_level_to_charge_; }
    /**
     * @brief 设置电源电量过低需要充电的电量
     */
    void SetBatteryLevelToCharge(int battery_level_to_charge);

    /**
     * @brief 获取电源电压过低需要关机的电压
     */
    float GetVoltageToShutdown() const { return voltage_to_shutdown_; }
    /**
     * @brief 设置电源电压过低需要关机的电压
     */
    void SetVoltageToShutdown(float voltage_to_shutdown);

    /**
     * @brief 获取电源电量过低需要关机的电量
     */
    int GetBatteryLevelToShutdown() const { return battery_level_to_shutdown_; }
    /**
     * @brief 设置电源电量过低需要关机的电量
     */
    void SetBatteryLevelToShutdown(int battery_level_to_shutdown);

    /**
     * @brief 获取电源温度过高需要报警的温度
     */
    float GetTempToWarning() const { return temp_to_warning_; }
    /**
     * @brief 设置电源温度过高需要报警的温度
     */
    void SetTempToWarning(float temp_to_warning);

    /**
     * @brief 是否正在充电
     */
    bool IsCharging() const { return is_charging_; }
    /**
     * @brief 是否温度过高需要报警
     */
    bool IsTooHotWarning() const { return is_too_hot_warning_; }

    /**
     * @brief 获取计数器: 进入充电模式的次数
     */
    size_t GetCntEnterCharge() const { return cnt_enter_charge_; }
    /**
     * @brief 获取计数器: 离开充电模式的次数
     */
    size_t GetCntLeaveCharge() const { return cnt_leave_charge_; }
    /**
     * @brief 获取计数器: 请求关机的次数
     */
    size_t GetCntShutdown() const { return cnt_shutdown_; }
    /**
     * @brief 获取计数器: 温度过高需要报警的次数
     */
    size_t GetCntEnterTooHot() const { return cnt_enter_too_hot_; }
    /**
     * @brief 获取计数器: 温度正常撤销报警的次数
     */
    size_t GetCntExitTooHot() const { return cnt_exit_too_hot_; }
    /**
     * @brief 获取计数器: 电量过低需要充电的次数
     */
    size_t GetCntNeedCharge() const { return cnt_need_charge_; }

    /**
     * @brief 获取当前电源类型
     */
    PowerSourceType GetCurrentPowerSource();

    /**
     * @brief 获取当前电源类型名称
     */
    const char* GetPowerSourceName(PowerSourceType type);
    /**
     * @brief 获取当前电源类型名称
     */
    const char* GetPowerSourceName();
};
