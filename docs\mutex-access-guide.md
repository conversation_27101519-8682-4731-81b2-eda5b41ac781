# 互斥访问解决方案指南

## 概述

在多线程环境中，当多个timer或任务同时访问共享资源（如指针）时，需要使用同步机制来避免竞态条件和数据不一致。本指南提供了三种主要的解决方案。

## 方案对比

| 方案 | 适用场景 | 性能 | 复杂度 | 推荐度 |
|------|----------|------|--------|--------|
| std::mutex | 通用场景，复杂数据结构 | 中等 | 低 | ⭐⭐⭐⭐⭐ |
| FreeRTOS信号量 | ESP32环境，需要超时控制 | 高 | 中等 | ⭐⭐⭐⭐ |
| 原子操作 | 简单数据类型，高性能要求 | 最高 | 高 | ⭐⭐⭐ |

## 方案1：std::mutex（推荐）

### 优点
- 标准C++，跨平台兼容
- 使用简单，RAII支持
- 自动异常安全

### 缺点
- 性能略低于FreeRTOS原生信号量
- 无超时控制

### 使用示例

```cpp
#include "examples/mutex_access_example.h"

// 创建线程安全的资源管理器
ThreadSafeResource<std::string> safe_resource;

// 在timer回调中安全访问
void timer_callback() {
    // 方法1：使用SafeExecute（推荐）
    safe_resource.SafeExecute([](std::shared_ptr<std::string> data) {
        if (data) {
            ESP_LOGI("Timer", "Data: %s", data->c_str());
            *data += " modified";
        }
    });
    
    // 方法2：获取资源副本
    auto data = safe_resource.GetResource();
    if (data) {
        // 安全操作数据
        ESP_LOGI("Timer", "Data: %s", data->c_str());
    }
}
```

### 完整示例

```cpp
void run_std_mutex_example() {
    TimerManager timer_mgr;
    auto shared_data = std::make_shared<std::string>("Initial Data");
    
    timer_mgr.SetSharedData(shared_data);
    timer_mgr.StartTimers();
    
    // 运行一段时间
    vTaskDelay(pdMS_TO_TICKS(10000));
    
    timer_mgr.StopTimers();
}
```

## 方案2：FreeRTOS信号量

### 优点
- ESP32原生支持，性能更好
- 支持超时控制
- 更细粒度的控制

### 缺点
- 平台相关
- 需要手动管理资源
- 代码稍复杂

### 使用示例

```cpp
#include "examples/freertos_mutex_example.h"

// 创建FreeRTOS线程安全资源
FreeRTOSThreadSafeResource<std::string> safe_resource;

// 在timer回调中使用（带超时）
void timer_callback() {
    // 使用50ms超时，避免在timer回调中阻塞太久
    bool success = safe_resource.SafeExecute([](std::shared_ptr<std::string> data) {
        ESP_LOGI("Timer", "Data: %s", data->c_str());
        *data += " [Timer]";
    }, pdMS_TO_TICKS(50));
    
    if (!success) {
        ESP_LOGW("Timer", "Failed to access resource (timeout)");
    }
}
```

### 读写锁示例

```cpp
// 适用于读多写少的场景
ReadWriteResource<int> rw_resource;

// 多个读者可以同时访问
void reader_task() {
    rw_resource.SafeRead([](std::shared_ptr<int> data) {
        ESP_LOGI("Reader", "Value: %d", *data);
    });
}

// 写者独占访问
void writer_task() {
    rw_resource.SafeWrite([](std::shared_ptr<int>& data) {
        if (data) {
            *data = 42;
            ESP_LOGI("Writer", "Updated value to %d", *data);
        }
    });
}
```

## 方案3：原子操作

### 优点
- 性能最高，无锁操作
- 适合高频访问场景
- 硬件级别的原子性保证

### 缺点
- 只适用于简单数据类型
- 复杂逻辑难以实现
- 容易出现ABA问题

### 使用示例

```cpp
#include "examples/atomic_access_example.h"

// 原子计数器
AtomicCounter counter;

void timer1_callback() {
    int new_value = counter.Increment();
    ESP_LOGI("Timer1", "Counter: %d", new_value);
}

void timer2_callback() {
    int old_value = counter.Get();
    if (counter.CompareAndSwap(old_value, old_value * 2)) {
        ESP_LOGI("Timer2", "Doubled counter from %d to %d", old_value, old_value * 2);
    }
}
```

### Lock-Free队列示例

```cpp
// 单生产者单消费者队列
LockFreeQueue<std::string> message_queue(100);

// 生产者（Timer1）
void producer_timer() {
    std::string message = "Message " + std::to_string(esp_timer_get_time());
    if (message_queue.Enqueue(message)) {
        ESP_LOGI("Producer", "Enqueued: %s", message.c_str());
    } else {
        ESP_LOGW("Producer", "Queue full!");
    }
}

// 消费者（Timer2）
void consumer_timer() {
    std::string message;
    if (message_queue.Dequeue(message)) {
        ESP_LOGI("Consumer", "Dequeued: %s", message.c_str());
    }
}
```

## 选择建议

### 1. 通用场景（推荐）
使用 **std::mutex** 方案：
- 代码简洁，易于维护
- 异常安全，RAII支持
- 跨平台兼容

### 2. ESP32高性能场景
使用 **FreeRTOS信号量** 方案：
- 需要超时控制
- 对性能有较高要求
- 读多写少场景可使用读写锁

### 3. 简单数据高频访问
使用 **原子操作** 方案：
- 简单计数器、标志位
- 高频率访问（如统计信息）
- 对延迟敏感的场景

## 最佳实践

### 1. 避免死锁
```cpp
// 错误：可能导致死锁
void bad_example() {
    mutex1.lock();
    mutex2.lock();  // 如果另一个线程先锁mutex2再锁mutex1，就会死锁
    // ...
    mutex2.unlock();
    mutex1.unlock();
}

// 正确：使用RAII和固定顺序
void good_example() {
    std::lock_guard<std::mutex> lock1(mutex1);
    std::lock_guard<std::mutex> lock2(mutex2);
    // 自动释放，避免死锁
}
```

### 2. 最小化临界区
```cpp
// 错误：临界区太大
void bad_critical_section() {
    std::lock_guard<std::mutex> lock(mutex);
    // 大量计算...
    expensive_computation();
    // 网络IO...
    network_request();
    shared_data = result;
}

// 正确：最小化临界区
void good_critical_section() {
    auto result = expensive_computation();
    auto response = network_request();
    
    {
        std::lock_guard<std::mutex> lock(mutex);
        shared_data = result;  // 只在必要时加锁
    }
}
```

### 3. Timer回调中的注意事项
```cpp
// Timer回调应该快速执行，避免长时间阻塞
void timer_callback(void* arg) {
    // 使用短超时
    if (safe_resource.SafeExecute([](auto data) {
        // 快速操作
        process_data(data);
    }, pdMS_TO_TICKS(10))) {  // 10ms超时
        // 成功处理
    } else {
        // 超时处理
        ESP_LOGW("Timer", "Resource access timeout");
    }
}
```

## 性能测试

可以使用提供的性能比较工具：

```cpp
#include "examples/atomic_access_example.h"

void test_performance() {
    PerformanceComparison::RunComparison();
}
```

这将比较不同方案在您的硬件上的性能表现。

## 总结

选择合适的互斥访问方案需要考虑：
1. **数据复杂度**：简单数据用原子操作，复杂数据用互斥锁
2. **访问频率**：高频访问优先考虑性能
3. **平台要求**：跨平台用std::mutex，ESP32专用可用FreeRTOS
4. **超时需求**：需要超时控制用FreeRTOS信号量
5. **维护成本**：优先选择简单易维护的方案

对于大多数场景，推荐使用 **std::mutex + ThreadSafeResource** 方案，它提供了最好的易用性和安全性平衡。
