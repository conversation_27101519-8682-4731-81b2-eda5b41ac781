#include "mutex_access_example.h"
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

const char* TimerManager::TAG = "TimerManager";
const char* TaskManager::TAG = "TaskManager";

// ============================================================================
// TimerManager 实现
// ============================================================================

TimerManager::TimerManager() 
    : timer1_handle_(nullptr)
    , timer2_handle_(nullptr) {
}

TimerManager::~TimerManager() {
    StopTimers();
}

bool TimerManager::StartTimers() {
    // 创建定时器1 - 每1秒执行一次
    esp_timer_create_args_t timer1_args = {
        .callback = Timer1Callback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "timer1",
        .skip_unhandled_events = false
    };
    
    esp_err_t ret = esp_timer_create(&timer1_args, &timer1_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create timer1: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 创建定时器2 - 每1.5秒执行一次
    esp_timer_create_args_t timer2_args = {
        .callback = Timer2Callback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "timer2",
        .skip_unhandled_events = false
    };
    
    ret = esp_timer_create(&timer2_args, &timer2_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create timer2: %s", esp_err_to_name(ret));
        esp_timer_delete(timer1_handle_);
        return false;
    }
    
    // 启动定时器
    esp_timer_start_periodic(timer1_handle_, 1000000); // 1秒 = 1,000,000微秒
    esp_timer_start_periodic(timer2_handle_, 1500000); // 1.5秒
    
    ESP_LOGI(TAG, "Timers started successfully");
    return true;
}

void TimerManager::StopTimers() {
    if (timer1_handle_) {
        esp_timer_stop(timer1_handle_);
        esp_timer_delete(timer1_handle_);
        timer1_handle_ = nullptr;
    }
    
    if (timer2_handle_) {
        esp_timer_stop(timer2_handle_);
        esp_timer_delete(timer2_handle_);
        timer2_handle_ = nullptr;
    }
    
    ESP_LOGI(TAG, "Timers stopped");
}

void TimerManager::SetSharedData(std::shared_ptr<std::string> data) {
    shared_data_.SetResource(data);
    ESP_LOGI(TAG, "Shared data updated");
}

void TimerManager::Timer1Callback(void* arg) {
    TimerManager* manager = static_cast<TimerManager*>(arg);
    manager->HandleTimer1();
}

void TimerManager::Timer2Callback(void* arg) {
    TimerManager* manager = static_cast<TimerManager*>(arg);
    manager->HandleTimer2();
}

void TimerManager::HandleTimer1() {
    // 方法1：使用SafeExecute（推荐）
    shared_data_.SafeExecute([this](std::shared_ptr<std::string> data) {
        if (data) {
            ESP_LOGI(TAG, "Timer1 accessing data: %s", data->c_str());
            *data += " [Timer1]";
        } else {
            ESP_LOGI(TAG, "Timer1: No data available");
        }
        return true;
    });
}

void TimerManager::HandleTimer2() {
    // 方法2：获取资源副本
    auto data = shared_data_.GetResource();
    if (data) {
        ESP_LOGI(TAG, "Timer2 accessing data: %s", data->c_str());
        
        // 注意：这里修改需要重新设置，因为我们操作的是副本
        *data += " [Timer2]";
        shared_data_.SetResource(data);
    } else {
        ESP_LOGI(TAG, "Timer2: No data available");
    }
}

// ============================================================================
// TaskManager 实现
// ============================================================================

TaskManager::TaskManager() 
    : task1_handle_(nullptr)
    , task2_handle_(nullptr)
    , should_stop_(false) {
}

TaskManager::~TaskManager() {
    StopTasks();
}

bool TaskManager::StartTasks() {
    should_stop_ = false;
    
    // 创建任务1
    BaseType_t ret = xTaskCreate(
        Task1Function,
        "task1",
        4096,
        this,
        5,
        &task1_handle_
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create task1");
        return false;
    }
    
    // 创建任务2
    ret = xTaskCreate(
        Task2Function,
        "task2",
        4096,
        this,
        5,
        &task2_handle_
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create task2");
        vTaskDelete(task1_handle_);
        return false;
    }
    
    ESP_LOGI(TAG, "Tasks started successfully");
    return true;
}

void TaskManager::StopTasks() {
    should_stop_ = true;
    
    if (task1_handle_) {
        vTaskDelete(task1_handle_);
        task1_handle_ = nullptr;
    }
    
    if (task2_handle_) {
        vTaskDelete(task2_handle_);
        task2_handle_ = nullptr;
    }
    
    ESP_LOGI(TAG, "Tasks stopped");
}

void TaskManager::SetSharedResource(std::shared_ptr<int> resource) {
    shared_resource_.SetResource(resource);
    ESP_LOGI(TAG, "Shared resource updated");
}

void TaskManager::Task1Function(void* arg) {
    TaskManager* manager = static_cast<TaskManager*>(arg);
    manager->HandleTask1();
}

void TaskManager::Task2Function(void* arg) {
    TaskManager* manager = static_cast<TaskManager*>(arg);
    manager->HandleTask2();
}

void TaskManager::HandleTask1() {
    while (!should_stop_) {
        // 安全访问共享资源
        shared_resource_.SafeExecute([this](std::shared_ptr<int> resource) {
            if (resource) {
                ESP_LOGI(TAG, "Task1 accessing resource: %d", *resource);
                (*resource)++;
            }
            return true;
        });
        
        vTaskDelay(pdMS_TO_TICKS(2000)); // 等待2秒
    }
}

void TaskManager::HandleTask2() {
    while (!should_stop_) {
        // 检查资源是否有效
        if (shared_resource_.IsValid()) {
            auto resource = shared_resource_.GetResource();
            if (resource) {
                ESP_LOGI(TAG, "Task2 accessing resource: %d", *resource);
                *resource *= 2;
                shared_resource_.SetResource(resource);
            }
        } else {
            ESP_LOGI(TAG, "Task2: Resource not available");
        }
        
        vTaskDelay(pdMS_TO_TICKS(3000)); // 等待3秒
    }
}
