#include "sd_card_manager.h"

const char* SDCardManager::TAG = "SDCardManager";

SDCardManager::SDCardManager(bool use_spi_mode)
    : status_(Status::NOT_INITIALIZED)
    , card_(nullptr)
    , use_spi_mode_(use_spi_mode)
    , status_callback_(nullptr)
{
    strcpy(mount_point_, "/sdcard");
    memset(&host_, 0, sizeof(host_));
}

SDCardManager::~SDCardManager() {
    Unmount();
}

bool SDCardManager::Initialize() {
    if (status_ != Status::NOT_INITIALIZED) {
        ESP_LOGW(TAG, "SD card already initialized");
        return true;
    }

    ESP_LOGI(TAG, "Initializing SD card");
    UpdateStatus(Status::INITIALIZING, "Initializing SD card...");

    if (use_spi_mode_) {
        // SPI模式初始化
        if (InitializeSPI()) {
            ESP_LOGI(TAG, "✅ SD card initialized in SPI mode");
            return Mount();
        }
    } else {
        // SDMMC模式
        if (InitializeSDMMC()) {
            ESP_LOGI(TAG, "✅ SD card initialized in SDMMC mode");
            return Mount();
        }
    }

    ESP_LOGE(TAG, "❌ Failed to initialize SD card");
    UpdateStatus(Status::ERROR, "SD card initialization failed");
    return false;
}

bool SDCardManager::InitializeSPI() {
    ESP_LOGI(TAG, "Initializing SD card in SPI mode");

    // SPI总线配置
    spi_bus_config_t bus_cfg = {
        .mosi_io_num = GPIO_NUM_11,
        .miso_io_num = GPIO_NUM_13,
        .sclk_io_num = GPIO_NUM_12,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = 4000,
    };

    esp_err_t ret = spi_bus_initialize(SPI2_HOST, &bus_cfg, SDSPI_DEFAULT_DMA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPI bus: %s", esp_err_to_name(ret));
        return false;
    }

    // SDSPI主机配置
    sdmmc_host_t host = SDSPI_HOST_DEFAULT();
    host.max_freq_khz = SDMMC_FREQ_DEFAULT;

    host_ = host;

    ESP_LOGI(TAG, "SPI mode configuration completed");
    return true;
}

bool SDCardManager::InitializeSDMMC() {
    ESP_LOGI(TAG, "Initializing SD card in SDMMC mode");

    // SDMMC主机配置
    sdmmc_host_t host = SDMMC_HOST_DEFAULT();
    host.flags = SDMMC_HOST_FLAG_1BIT;
    host.max_freq_khz = SDMMC_FREQ_DEFAULT;

    host_ = host;

    ESP_LOGI(TAG, "SDMMC mode configuration completed");
    return true;
}

bool SDCardManager::Mount(bool format) {
    if (status_ == Status::MOUNTED) {
        ESP_LOGW(TAG, "SD card already mounted");
        return true;
    }

    ESP_LOGI(TAG, "Mounting SD card to %s", mount_point_);

    // 挂载配置
    esp_vfs_fat_sdmmc_mount_config_t mount_config = {
        .format_if_mount_failed = format,  // 如果挂载失败则格式化
        .max_files = 10,                 // 增加最大文件数
        .allocation_unit_size = 16 * 1024,
        .disk_status_check_enable = false // 禁用磁盘状态检查以提高性能
    };

    esp_err_t ret;
    if (use_spi_mode_) {
        // SPI模式挂载
        sdspi_device_config_t slot_config = SDSPI_DEVICE_CONFIG_DEFAULT();
        slot_config.gpio_cs = GPIO_NUM_10;
        slot_config.host_id = SPI2_HOST;

        ret = esp_vfs_fat_sdspi_mount(mount_point_, &host_, &slot_config, &mount_config, &card_);
    } else {
        // SDMMC模式挂载 - 自定义引脚配置避免与LCD冲突
        sdmmc_slot_config_t slot_config = SDMMC_SLOT_CONFIG_DEFAULT();

        // ESP32-S3的SDMMC_SLOT_CONFIG_DEFAULT默认引脚可能包含：
        // CLK: GPIO_36, CMD: GPIO_35, D0: GPIO_37, D1: GPIO_38, D2: GPIO_33, D3: GPIO_34
        // 或者其他可能与LCD冲突的引脚，因此我们明确设置安全的引脚

        slot_config.width = 1;          // 使用1线模式，只需要CLK, CMD, D0
        slot_config.clk = GPIO_NUM_2;   // 安全引脚，不与LCD冲突
        slot_config.cmd = GPIO_NUM_1;   // 安全引脚，不与LCD冲突
        slot_config.d0 = GPIO_NUM_3;    // 安全引脚，不与LCD冲突

        // 明确禁用其他数据线，确保不会意外使用冲突引脚
        slot_config.d1 = GPIO_NUM_NC;
        slot_config.d2 = GPIO_NUM_NC;
        slot_config.d3 = GPIO_NUM_NC;
        slot_config.d4 = GPIO_NUM_NC;
        slot_config.d5 = GPIO_NUM_NC;
        slot_config.d6 = GPIO_NUM_NC;
        slot_config.d7 = GPIO_NUM_NC;

        // 禁用卡检测和写保护引脚
        slot_config.cd = GPIO_NUM_NC;
        slot_config.wp = GPIO_NUM_NC;

        // Enable internal pullups on enabled pins. The internal pullups
        // are insufficient however, please make sure 10k external pullups are
        // connected on the bus. This is for debug / example purpose only.
        slot_config.flags |= SDMMC_SLOT_FLAG_INTERNAL_PULLUP;

        // 验证引脚配置，确保没有冲突
        ESP_LOGI(TAG, "SDMMC pin configuration:");
        ESP_LOGI(TAG, "  CLK: GPIO_%d", slot_config.clk);
        ESP_LOGI(TAG, "  CMD: GPIO_%d", slot_config.cmd);
        ESP_LOGI(TAG, "  D0:  GPIO_%d", slot_config.d0);
        if (slot_config.d1 == GPIO_NUM_NC) {
            ESP_LOGI(TAG, "  D1:  NC");
        } else {
            ESP_LOGI(TAG, "  D1:  GPIO_%d", slot_config.d1);
        }
        if (slot_config.d2 == GPIO_NUM_NC) {
            ESP_LOGI(TAG, "  D2:  NC");
        } else {
            ESP_LOGI(TAG, "  D2:  GPIO_%d", slot_config.d2);
        }
        if (slot_config.d3 == GPIO_NUM_NC) {
            ESP_LOGI(TAG, "  D3:  NC");
        } else {
            ESP_LOGI(TAG, "  D3:  GPIO_%d", slot_config.d3);
        }

        // 检查与LCD引脚的冲突 (LCD使用: 4,5,6,7,12,38,39)
        const int lcd_pins[] = {4, 5, 6, 7, 12, 38, 39};
        const int sdmmc_pins[] = {slot_config.clk, slot_config.cmd, slot_config.d0};

        bool conflict_found = false;
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 7; j++) {
                if (sdmmc_pins[i] == lcd_pins[j]) {
                    ESP_LOGE(TAG, "⚠️ PIN CONFLICT: SDMMC GPIO_%d conflicts with LCD!", sdmmc_pins[i]);
                    conflict_found = true;
                }
            }
        }

        if (!conflict_found) {
            ESP_LOGI(TAG, "✅ No pin conflicts detected between SDMMC and LCD");
        }

        ret = esp_vfs_fat_sdmmc_mount(mount_point_, &host_, &slot_config, &mount_config, &card_);
    }

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to mount SD card: %s", esp_err_to_name(ret));
        
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "Failed to mount filesystem. If you want to format the card, set format_if_mount_failed = true");
        } else if (ret == ESP_ERR_NOT_FOUND) {
            ESP_LOGE(TAG, "Failed to find SD card");
        }
        
        UpdateStatus(Status::ERROR, "SD card mount failed");

        // 运行诊断
        ESP_LOGI(TAG, "Running mount diagnostics...");
        DiagnoseMountIssues();

        return false;
    }

    UpdateStatus(Status::MOUNTED, "SD card mounted successfully");

    // 打印卡片信息
    PrintCardInfo();

    ESP_LOGI(TAG, "✅ SD card mounted successfully");
    return true;
}

bool SDCardManager::Unmount() {
    if (status_ != Status::MOUNTED) {
        return true;
    }

    ESP_LOGI(TAG, "Unmounting SD card");

    esp_err_t ret = esp_vfs_fat_sdcard_unmount(mount_point_, card_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to unmount SD card: %s", esp_err_to_name(ret));
        return false;
    }

    card_ = nullptr;
    UpdateStatus(Status::UNMOUNTED, "SD card unmounted");
    
    ESP_LOGI(TAG, "SD card unmounted");
    return true;
}

bool SDCardManager::Remount() {
    ESP_LOGI(TAG, "Remounting SD card...");

    // 先卸载
    if (status_ == Status::MOUNTED) {
        if (!Unmount()) {
            ESP_LOGE(TAG, "Failed to unmount before remount");
            return false;
        }
    }

    // 等待一下
    vTaskDelay(pdMS_TO_TICKS(100));

    // 重新挂载
    return Mount();
}

bool SDCardManager::TryFormatAndRemount() {
    ESP_LOGW(TAG, "Attempting to format and remount SD card...");
    ESP_LOGW(TAG, "⚠️  This will erase all data on the SD card!");

    // 先卸载
    if (status_ == Status::MOUNTED) {
        Unmount();
    }

    // 尝试使用格式化选项重新挂载
    ESP_LOGI(TAG, "Mounting with format_if_mount_failed = true");

    // 等待一下
    vTaskDelay(pdMS_TO_TICKS(100));

    // 重新挂载
    return Mount(true);
}

bool SDCardManager::GetCardInfo(CardInfo& info) const {
    if (!card_) {
        return false;
    }

    strncpy(info.name, card_->cid.name, sizeof(info.name) - 1);
    info.name[sizeof(info.name) - 1] = '\0';
    
    info.size_bytes = ((uint64_t)card_->csd.capacity) * card_->csd.sector_size;
    info.sector_size = card_->csd.sector_size;
    info.sector_count = card_->csd.capacity;
    info.csd_ver = card_->csd.csd_ver;
    info.mmc_ver = card_->scr.sd_spec;
    info.frequency_mhz = card_->real_freq_khz / 1000.0f;
    info.is_mmc = (card_->ocr & SD_OCR_SDHC_CAP) == 0;

    return true;
}

uint64_t SDCardManager::GetTotalSize() const {
    if (!card_) {
        return 0;
    }
    return ((uint64_t)card_->csd.capacity) * card_->csd.sector_size;
}

uint64_t SDCardManager::GetFreeSize() const {
    if (status_ != Status::MOUNTED) {
        return 0;
    }

    FATFS* fs;
    DWORD free_clusters;
    
    if (f_getfree("0:", &free_clusters, &fs) != FR_OK) {
        return 0;
    }

    return (uint64_t)free_clusters * fs->csize * 512;
}

uint64_t SDCardManager::GetUsedSize() const {
    uint64_t total = GetTotalSize();
    uint64_t free = GetFreeSize();
    return (total > free) ? (total - free) : 0;
}

void SDCardManager::UpdateStatus(Status new_status, const char* message) {
    if (status_ != new_status) {
        Status old_status = status_;
        status_ = new_status;
        
        ESP_LOGI(TAG, "Status changed: %s -> %s", 
                GetStatusName(old_status), GetStatusName());
        
        if (status_callback_) {
            status_callback_(new_status, message);
        }
    }
}

const char* SDCardManager::GetStatusName(Status s) const {
    switch (s) {
        case Status::NOT_INITIALIZED: return "NOT_INITIALIZED";
        case Status::INITIALIZING: return "INITIALIZING";
        case Status::MOUNTED: return "MOUNTED";
        case Status::UNMOUNTED: return "UNMOUNTED";
        case Status::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

const char* SDCardManager::GetStatusName() const {
    return GetStatusName(status_);
}

// 文件操作实现
bool SDCardManager::WriteFile(const char* path, const void* data, size_t size) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path || !data || size == 0) {
        ESP_LOGE(TAG, "Invalid parameters for WriteFile");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGI(TAG, "Writing file: %s (%d bytes)", full_path, size);

    FILE* file = fopen(full_path, "wb");
    if (!file) {
        ESP_LOGE(TAG, "Failed to open file for writing: %s", full_path);
        return false;
    }

    size_t written = fwrite(data, 1, size, file);
    fclose(file);

    if (written != size) {
        ESP_LOGE(TAG, "Failed to write complete data: %d/%d bytes", written, size);
        return false;
    }

    ESP_LOGI(TAG, "✅ File written successfully: %s", full_path);
    return true;
}

bool SDCardManager::ReadFile(const char* path, void* buffer, size_t buffer_size, size_t* bytes_read) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path || !buffer || buffer_size == 0) {
        ESP_LOGE(TAG, "Invalid parameters for ReadFile");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGI(TAG, "Reading file: %s", full_path);

    FILE* file = fopen(full_path, "rb");
    if (!file) {
        ESP_LOGE(TAG, "Failed to open file for reading: %s", full_path);
        return false;
    }

    size_t read_bytes = fread(buffer, 1, buffer_size, file);
    fclose(file);

    if (bytes_read) {
        *bytes_read = read_bytes;
    }

    ESP_LOGI(TAG, "✅ File read successfully: %s (%d bytes)", full_path, read_bytes);
    return true;
}

bool SDCardManager::AppendFile(const char* path, const void* data, size_t size) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path || !data || size == 0) {
        ESP_LOGE(TAG, "Invalid parameters for AppendFile");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGI(TAG, "Appending to file: %s (%d bytes)", full_path, size);

    FILE* file = fopen(full_path, "ab");
    if (!file) {
        ESP_LOGE(TAG, "Failed to open file for appending: %s", full_path);
        return false;
    }

    size_t written = fwrite(data, 1, size, file);
    fclose(file);

    if (written != size) {
        ESP_LOGE(TAG, "Failed to append complete data: %d/%d bytes", written, size);
        return false;
    }

    ESP_LOGI(TAG, "✅ Data appended successfully: %s", full_path);
    return true;
}

bool SDCardManager::DeleteFile(const char* path) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path) {
        ESP_LOGE(TAG, "Invalid path for DeleteFile");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGI(TAG, "Deleting file: %s", full_path);

    if (remove(full_path) != 0) {
        ESP_LOGE(TAG, "Failed to delete file: %s", full_path);
        return false;
    }

    ESP_LOGI(TAG, "✅ File deleted successfully: %s", full_path);
    return true;
}

bool SDCardManager::FileExists(const char* path) {
    if (status_ != Status::MOUNTED) {
        return false;
    }

    if (!path) {
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    // 尝试打开文件来检查是否存在
    FILE* file = fopen(full_path, "r");
    if (file) {
        fclose(file);
        return true;
    }
    return false;
}

size_t SDCardManager::GetFileSize(const char* path) {
    if (status_ != Status::MOUNTED) {
        return 0;
    }

    if (!path) {
        return 0;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    FILE* file = fopen(full_path, "r");
    if (!file) {
        return 0;
    }

    fseek(file, 0, SEEK_END);
    size_t size = ftell(file);
    fclose(file);

    return size;
}

void SDCardManager::GetFullPath(const char* relative_path, char* full_path, size_t full_path_size) const {
    if (relative_path && relative_path[0] == '/') {
        // 绝对路径，添加挂载点
        snprintf(full_path, full_path_size, "%s%s", mount_point_, relative_path);
    } else {
        // 相对路径，添加挂载点和斜杠
        snprintf(full_path, full_path_size, "%s/%s", mount_point_, relative_path ? relative_path : "");
    }
}

bool SDCardManager::CreateDirectoryRecursive(const char* full_path) {
    if (!full_path) {
        return false;
    }

    // 检查目录是否已存在
    struct stat st;
    if (stat(full_path, &st) == 0) {
        if (S_ISDIR(st.st_mode)) {
            ESP_LOGD(TAG, "Directory already exists: %s", full_path);
            return true;
        } else {
            ESP_LOGE(TAG, "Path exists but is not a directory: %s", full_path);
            return false;
        }
    }

    // 尝试创建目录
    if (mkdir(full_path, 0777) == 0) {
        ESP_LOGD(TAG, "Directory created: %s", full_path);
        return true;
    }

    // 如果失败，检查错误类型
    int err = errno;
    if (err == EEXIST) {
        // 目录已存在（可能是并发创建）
        ESP_LOGD(TAG, "Directory exists (concurrent creation): %s", full_path);
        return true;
    } else if (err == ENOENT) {
        // 父目录不存在，递归创建父目录
        ESP_LOGD(TAG, "Parent directory missing, creating recursively: %s", full_path);

        // 复制路径并找到父目录
        char parent_path[512];
        strncpy(parent_path, full_path, sizeof(parent_path) - 1);
        parent_path[sizeof(parent_path) - 1] = '\0';

        // 找到最后一个斜杠
        char* last_slash = strrchr(parent_path, '/');
        if (last_slash && last_slash != parent_path) {
            *last_slash = '\0';

            // 递归创建父目录
            if (!CreateDirectoryRecursive(parent_path)) {
                ESP_LOGE(TAG, "Failed to create parent directory: %s", parent_path);
                return false;
            }

            // 再次尝试创建目录
            if (mkdir(full_path, 0777) == 0) {
                ESP_LOGD(TAG, "Directory created after parent: %s", full_path);
                return true;
            } else {
                ESP_LOGE(TAG, "Failed to create directory after creating parent: %s, errno: %d",
                        full_path, errno);
                return false;
            }
        } else {
            ESP_LOGE(TAG, "Cannot determine parent directory: %s", full_path);
            return false;
        }
    } else {
        ESP_LOGE(TAG, "Failed to create directory: %s, errno: %d",
                full_path, errno);
        return false;
    }
}

bool SDCardManager::DeleteDirectoryRecursive(const char* full_path) {
    if (!full_path) {
        return false;
    }

    ESP_LOGD(TAG, "Recursively deleting directory: %s", full_path);

    DIR* dir = opendir(full_path);
    if (!dir) {
        ESP_LOGE(TAG, "Failed to open directory for deletion: %s", full_path);
        return false;
    }

    struct dirent* entry;
    bool success = true;

    while ((entry = readdir(dir)) != nullptr) {
        // 跳过 . 和 .. 目录
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        char entry_path[512];
        snprintf(entry_path, sizeof(entry_path), "%s/%s", full_path, entry->d_name);

        struct stat st;
        if (stat(entry_path, &st) == 0) {
            if (S_ISDIR(st.st_mode)) {
                // 递归删除子目录
                if (!DeleteDirectoryRecursive(entry_path)) {
                    ESP_LOGE(TAG, "Failed to delete subdirectory: %s", entry_path);
                    success = false;
                    break;
                }
            } else {
                // 删除文件
                if (unlink(entry_path) != 0) {
                    ESP_LOGE(TAG, "Failed to delete file: %s", entry_path);
                    success = false;
                    break;
                }
                ESP_LOGD(TAG, "Deleted file: %s", entry_path);
            }
        } else {
            ESP_LOGW(TAG, "Cannot stat entry: %s", entry_path);
        }
    }

    closedir(dir);

    if (success) {
        // 删除空目录
        if (rmdir(full_path) == 0) {
            ESP_LOGD(TAG, "Deleted directory: %s", full_path);
            return true;
        } else {
            ESP_LOGE(TAG, "Failed to remove directory: %s", full_path);
            return false;
        }
    }

    return false;
}

// 文本文件操作
bool SDCardManager::WriteTextFile(const char* path, const char* text) {
    if (!text) {
        ESP_LOGE(TAG, "Invalid text for WriteTextFile");
        return false;
    }
    return WriteFile(path, text, strlen(text));
}

bool SDCardManager::ReadTextFile(const char* path, char* buffer, size_t buffer_size) {
    if (!buffer || buffer_size == 0) {
        ESP_LOGE(TAG, "Invalid buffer for ReadTextFile");
        return false;
    }

    size_t bytes_read;
    if (!ReadFile(path, buffer, buffer_size - 1, &bytes_read)) {
        return false;
    }

    buffer[bytes_read] = '\0'; // 确保字符串结束
    return true;
}

bool SDCardManager::AppendTextFile(const char* path, const char* text) {
    if (!text) {
        ESP_LOGE(TAG, "Invalid text for AppendTextFile");
        return false;
    }
    return AppendFile(path, text, strlen(text));
}

// 目录操作
bool SDCardManager::CreateDirectory(const char* path) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path) {
        ESP_LOGE(TAG, "Invalid path for CreateDirectory");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGI(TAG, "Creating directory: %s", full_path);

    // 检查挂载点是否可写
    char test_file[600];
    snprintf(test_file, sizeof(test_file), "%s/.write_test", mount_point_);

    ESP_LOGI(TAG, "Testing write access to mount point: %s", mount_point_);

    FILE* test = fopen(test_file, "w");
    if (test) {
        // 尝试写入一些数据
        const char* test_data = "write_test";
        size_t written = fwrite(test_data, 1, strlen(test_data), test);
        fclose(test);

        if (written == strlen(test_data)) {
            // 验证文件是否真的被创建
            FILE* verify = fopen(test_file, "r");
            if (verify) {
                fclose(verify);
                remove(test_file);
                ESP_LOGI(TAG, "✅ Mount point is writable: %s", mount_point_);
            } else {
                ESP_LOGE(TAG, "❌ File created but cannot be read back");
                return false;
            }
        } else {
            ESP_LOGE(TAG, "❌ Failed to write data to test file");
            remove(test_file);
            return false;
        }
    } else {
        ESP_LOGE(TAG, "❌ Mount point is not writable: %s", mount_point_);
        ESP_LOGE(TAG, "Possible causes:");
        ESP_LOGE(TAG, "1. SD card is write-protected");
        ESP_LOGE(TAG, "2. File system is read-only");
        ESP_LOGE(TAG, "3. SD card is corrupted");
        ESP_LOGE(TAG, "4. Insufficient permissions");

        // 尝试获取更多信息
        struct stat st;
        if (stat(mount_point_, &st) == 0) {
            ESP_LOGI(TAG, "Mount point exists, mode: 0%lo", st.st_mode);
        } else {
            ESP_LOGE(TAG, "Cannot stat mount point: %s", mount_point_);
        }

        return false;
    }

    // 使用递归创建目录的辅助函数
    if (CreateDirectoryRecursive(full_path)) {
        ESP_LOGI(TAG, "✅ Directory created successfully: %s", full_path);
        return true;
    } else {
        ESP_LOGE(TAG, "❌ Failed to create directory: %s", full_path);
        return false;
    }

    // 简化版本：尝试在目录中创建一个临时文件来测试目录是否可创建
    snprintf(test_file, sizeof(test_file), "%s/.test", full_path);

    FILE* file = fopen(test_file, "w");
    if (file) {
        fclose(file);
        remove(test_file);
        ESP_LOGI(TAG, "✅ Directory accessible: %s", full_path);
        return true;
    }

    ESP_LOGW(TAG, "Directory may not exist or not accessible: %s", full_path);
    return false;
}

bool SDCardManager::DeleteDirectory(const char* path) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path) {
        ESP_LOGE(TAG, "Invalid path for DeleteDirectory");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGI(TAG, "Deleting directory: %s", full_path);

    // 递归删除目录内容
    if (!DeleteDirectoryRecursive(full_path)) {
        ESP_LOGE(TAG, "Failed to delete directory contents: %s", full_path);
        return false;
    }

    // 删除空目录
    if (rmdir(full_path) != 0) {
        ESP_LOGE(TAG, "Failed to remove directory: %s", full_path);
        return false;
    }

    ESP_LOGI(TAG, "✅ Directory deleted successfully: %s", full_path);
    return true;
}

bool SDCardManager::DirectoryExists(const char* path) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!path) {
        ESP_LOGE(TAG, "Invalid path for DirectoryExists");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGD(TAG, "Checking if directory exists: %s", full_path);

    struct stat st;
    if (stat(full_path, &st) == 0) {
        bool is_dir = S_ISDIR(st.st_mode);
        ESP_LOGD(TAG, "Path %s exists and is %s", full_path, is_dir ? "directory" : "file");
        return is_dir;
    }

    ESP_LOGD(TAG, "Directory does not exist: %s", full_path);
    return false;
}

bool SDCardManager::ListDirectory(const char* path, FileInfo* files, size_t max_files, size_t* file_count) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!files || max_files == 0) {
        ESP_LOGE(TAG, "Invalid parameters for ListDirectory");
        return false;
    }

    char full_path[512];
    GetFullPath(path, full_path, sizeof(full_path));

    ESP_LOGD(TAG, "Listing directory: %s", full_path);

    DIR* dir = opendir(full_path);
    if (!dir) {
        ESP_LOGE(TAG, "Failed to open directory: %s", full_path);
        return false;
    }

    size_t count = 0;
    struct dirent* entry;

    while ((entry = readdir(dir)) != nullptr && count < max_files) {
        // 跳过 . 和 .. 目录
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        FileInfo& info = files[count];
        strncpy(info.name, entry->d_name, sizeof(info.name) - 1);
        info.name[sizeof(info.name) - 1] = '\0';

        // 获取文件详细信息
        char file_full_path[800];
        snprintf(file_full_path, sizeof(file_full_path), "%s/%s", full_path, entry->d_name);

        struct stat st;
        if (stat(file_full_path, &st) == 0) {
            info.size = st.st_size;
            info.is_directory = S_ISDIR(st.st_mode);
            info.modified_time = st.st_mtime;
        } else {
            info.size = 0;
            info.is_directory = (entry->d_type == DT_DIR);
            info.modified_time = 0;
        }

        count++;
    }

    closedir(dir);

    if (file_count) {
        *file_count = count;
    }

    ESP_LOGI(TAG, "✅ Directory listed: %d files/directories", count);
    return true;
}

// 高级文件操作
bool SDCardManager::CopyFile(const char* src_path, const char* dst_path, ProgressCallback progress) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!src_path || !dst_path) {
        ESP_LOGE(TAG, "Invalid paths for CopyFile");
        return false;
    }

    char src_full_path[512], dst_full_path[512];
    GetFullPath(src_path, src_full_path, sizeof(src_full_path));
    GetFullPath(dst_path, dst_full_path, sizeof(dst_full_path));

    ESP_LOGI(TAG, "Copying file: %s -> %s", src_full_path, dst_full_path);

    FILE* src_file = fopen(src_full_path, "rb");
    if (!src_file) {
        ESP_LOGE(TAG, "Failed to open source file: %s", src_full_path);
        return false;
    }

    FILE* dst_file = fopen(dst_full_path, "wb");
    if (!dst_file) {
        ESP_LOGE(TAG, "Failed to open destination file: %s", dst_full_path);
        fclose(src_file);
        return false;
    }

    // 获取文件大小
    fseek(src_file, 0, SEEK_END);
    size_t file_size = ftell(src_file);
    fseek(src_file, 0, SEEK_SET);

    // 复制文件
    uint8_t buffer[COPY_BUFFER_SIZE];
    size_t total_copied = 0;
    size_t bytes_read;

    while ((bytes_read = fread(buffer, 1, sizeof(buffer), src_file)) > 0) {
        size_t bytes_written = fwrite(buffer, 1, bytes_read, dst_file);
        if (bytes_written != bytes_read) {
            ESP_LOGE(TAG, "Failed to write data during copy");
            fclose(src_file);
            fclose(dst_file);
            remove(dst_full_path); // 删除不完整的文件
            return false;
        }

        total_copied += bytes_written;

        // 进度回调
        if (progress) {
            progress(total_copied, file_size);
        }
    }

    fclose(src_file);
    fclose(dst_file);

    ESP_LOGI(TAG, "✅ File copied successfully: %d bytes", total_copied);
    return true;
}

bool SDCardManager::MoveFile(const char* src_path, const char* dst_path) {
    if (!CopyFile(src_path, dst_path)) {
        return false;
    }
    return DeleteFile(src_path);
}

bool SDCardManager::RenameFile(const char* old_path, const char* new_path) {
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "SD card not mounted");
        return false;
    }

    if (!old_path || !new_path) {
        ESP_LOGE(TAG, "Invalid paths for RenameFile");
        return false;
    }

    char old_full_path[512], new_full_path[512];
    GetFullPath(old_path, old_full_path, sizeof(old_full_path));
    GetFullPath(new_path, new_full_path, sizeof(new_full_path));

    ESP_LOGI(TAG, "Renaming file: %s -> %s", old_full_path, new_full_path);

    if (rename(old_full_path, new_full_path) != 0) {
        ESP_LOGE(TAG, "Failed to rename file");
        return false;
    }

    ESP_LOGI(TAG, "✅ File renamed successfully");
    return true;
}

// 日志文件操作
bool SDCardManager::WriteLog(const char* filename, const char* message, bool with_timestamp) {
    if (!filename || !message) {
        return false;
    }

    char log_entry[1024];
    if (with_timestamp) {
        time_t now = time(nullptr);
        struct tm* timeinfo = localtime(&now);

        if (timeinfo) {
            char timestamp[64];
            strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", timeinfo);
            snprintf(log_entry, sizeof(log_entry), "[%s] %s\n", timestamp, message);
        } else {
            // 如果无法获取时间，使用系统滴答计数作为备选
            uint32_t tick = esp_timer_get_time() / 1000; // 毫秒
            snprintf(log_entry, sizeof(log_entry), "[%lu] %s\n", (unsigned long)tick, message);
        }
    } else {
        snprintf(log_entry, sizeof(log_entry), "%s\n", message);
    }

    return AppendTextFile(filename, log_entry);
}

bool SDCardManager::ClearLog(const char* filename) {
    if (!filename) {
        return false;
    }
    return WriteTextFile(filename, "");
}

// JSON文件操作
bool SDCardManager::WriteJsonFile(const char* path, const char* json_string) {
    if (!json_string) {
        ESP_LOGE(TAG, "Invalid JSON string");
        return false;
    }
    return WriteTextFile(path, json_string);
}

bool SDCardManager::ReadJsonFile(const char* path, char* buffer, size_t buffer_size) {
    return ReadTextFile(path, buffer, buffer_size);
}

// 调试输出
void SDCardManager::PrintCardInfo() const {
    if (!card_) {
        ESP_LOGW(TAG, "No card information available");
        return;
    }

    ESP_LOGI(TAG, "=== SD Card Information ===");
    ESP_LOGI(TAG, "Name: %s", card_->cid.name);
    ESP_LOGI(TAG, "Type: %s", (card_->ocr & SD_OCR_SDHC_CAP) ? "SDHC/SDXC" : "SDSC");
    ESP_LOGI(TAG, "Speed: %.1f MHz", card_->real_freq_khz / 1000.0f);
    ESP_LOGI(TAG, "Size: %.2f GB", GetTotalSize() / (1024.0 * 1024.0 * 1024.0));
    ESP_LOGI(TAG, "Sector size: %d bytes", card_->csd.sector_size);
    ESP_LOGI(TAG, "Sectors: %d", card_->csd.capacity);
    ESP_LOGI(TAG, "CSD version: %d", card_->csd.csd_ver + 1);
    ESP_LOGI(TAG, "===========================");
}

void SDCardManager::DiagnoseMountIssues() const {
    ESP_LOGI(TAG, "=== SD Card Mount Diagnostics ===");

    // 检查卡片状态
    if (!card_) {
        ESP_LOGE(TAG, "❌ No card detected");
        ESP_LOGI(TAG, "Possible solutions:");
        ESP_LOGI(TAG, "1. Check SD card is properly inserted");
        ESP_LOGI(TAG, "2. Check SD card contacts");
        ESP_LOGI(TAG, "3. Try a different SD card");
        return;
    }

    ESP_LOGI(TAG, "✅ Card detected: %s", card_->cid.name);

    // 检查挂载状态
    if (status_ != Status::MOUNTED) {
        ESP_LOGE(TAG, "❌ Card not mounted, status: %s", GetStatusName());
        return;
    }

    ESP_LOGI(TAG, "✅ Card mounted at: %s", mount_point_);

    // 检查挂载点目录
    struct stat st;
    if (stat(mount_point_, &st) == 0) {
        ESP_LOGI(TAG, "✅ Mount point exists");
        ESP_LOGI(TAG, "Mount point mode: 0%lo", st.st_mode);
        ESP_LOGI(TAG, "Is directory: %s", S_ISDIR(st.st_mode) ? "Yes" : "No");
    } else {
        ESP_LOGE(TAG, "❌ Mount point does not exist: %s", mount_point_);
        return;
    }

    // 检查文件系统信息
    FATFS* fs;
    DWORD free_clusters;
    if (f_getfree("0:", &free_clusters, &fs) == FR_OK) {
        ESP_LOGI(TAG, "✅ File system accessible");
        ESP_LOGI(TAG, "Cluster size: %d bytes", fs->csize * 512);
        ESP_LOGI(TAG, "Free clusters: %lu", free_clusters);
        ESP_LOGI(TAG, "File system type: FAT%d", fs->fs_type == FS_FAT12 ? 12 :
                                                  fs->fs_type == FS_FAT16 ? 16 : 32);
    } else {
        ESP_LOGE(TAG, "❌ Cannot access file system");
    }

    // 测试基本写入
    char test_file[256];
    snprintf(test_file, sizeof(test_file), "%s/.mount_test", mount_point_);

    FILE* test = fopen(test_file, "w");
    if (test) {
        const char* test_data = "mount_test_data";
        size_t written = fwrite(test_data, 1, strlen(test_data), test);
        fclose(test);

        if (written == strlen(test_data)) {
            ESP_LOGI(TAG, "✅ Write test successful");
            remove(test_file);
        } else {
            ESP_LOGE(TAG, "❌ Write test failed: only %d bytes written", written);
        }
    } else {
        ESP_LOGE(TAG, "❌ Cannot create test file");
        ESP_LOGI(TAG, "Troubleshooting steps:");
        ESP_LOGI(TAG, "1. Check SD card write protection switch");
        ESP_LOGI(TAG, "2. Try formatting the SD card (FAT32)");
        ESP_LOGI(TAG, "3. Check SD card for errors on PC");
        ESP_LOGI(TAG, "4. Try a different SD card");
    }

    ESP_LOGI(TAG, "=== Diagnostics Complete ===");
}


// SDCardUtils实现
const char* SDCardUtils::GetFileExtension(const char* filename) {
    if (!filename) {
        return "";
    }

    const char* dot = strrchr(filename, '.');
    return dot ? dot + 1 : "";
}

bool SDCardUtils::IsTextFile(const char* filename) {
    const char* ext = GetFileExtension(filename);
    return (strcasecmp(ext, "txt") == 0 ||
            strcasecmp(ext, "log") == 0 ||
            strcasecmp(ext, "json") == 0 ||
            strcasecmp(ext, "xml") == 0 ||
            strcasecmp(ext, "csv") == 0);
}

bool SDCardUtils::IsImageFile(const char* filename) {
    const char* ext = GetFileExtension(filename);
    return (strcasecmp(ext, "jpg") == 0 ||
            strcasecmp(ext, "jpeg") == 0 ||
            strcasecmp(ext, "png") == 0 ||
            strcasecmp(ext, "bmp") == 0 ||
            strcasecmp(ext, "gif") == 0);
}

bool SDCardUtils::IsAudioFile(const char* filename) {
    const char* ext = GetFileExtension(filename);
    return (strcasecmp(ext, "mp3") == 0 ||
            strcasecmp(ext, "wav") == 0 ||
            strcasecmp(ext, "flac") == 0 ||
            strcasecmp(ext, "aac") == 0 ||
            strcasecmp(ext, "ogg") == 0);
}

void SDCardUtils::GetFileName(const char* path, char* filename, size_t filename_size) {
    if (!path || !filename || filename_size == 0) {
        return;
    }

    const char* last_slash = strrchr(path, '/');
    const char* name = last_slash ? last_slash + 1 : path;
    strncpy(filename, name, filename_size - 1);
    filename[filename_size - 1] = '\0';
}

void SDCardUtils::FormatSize(uint64_t size_bytes, char* buffer, size_t buffer_size) {
    if (!buffer || buffer_size == 0) {
        return;
    }

    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = (double)size_bytes;

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    if (unit_index == 0) {
        snprintf(buffer, buffer_size, "%.0f %s", size, units[unit_index]);
    } else {
        snprintf(buffer, buffer_size, "%.2f %s", size, units[unit_index]);
    }
}

void SDCardUtils::FormatTime(time_t timestamp, char* buffer, size_t buffer_size) {
    if (!buffer || buffer_size == 0) {
        return;
    }

    if (timestamp == 0) {
        snprintf(buffer, buffer_size, "Unknown");
        return;
    }

    struct tm* timeinfo = localtime(&timestamp);
    if (timeinfo) {
        strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", timeinfo);
    } else {
        snprintf(buffer, buffer_size, "Invalid time");
    }
}

void SDCardUtils::ShowSDCardInfo(SDCardManager& sd) {
    ESP_LOGI("SDCardUtils", "=== SD Card Status ===");
    ESP_LOGI("SDCardUtils", "Status: %s", sd.GetStatusName());
    ESP_LOGI("SDCardUtils", "Mount Point: %s", sd.GetMountPoint());

    if (sd.IsMounted()) {
        char size_str[32];
        FormatSize(sd.GetTotalSize(), size_str, sizeof(size_str));
        ESP_LOGI("SDCardUtils", "Total Size: %s", size_str);

        FormatSize(sd.GetFreeSize(), size_str, sizeof(size_str));
        ESP_LOGI("SDCardUtils", "Free Size: %s", size_str);

        FormatSize(sd.GetUsedSize(), size_str, sizeof(size_str));
        ESP_LOGI("SDCardUtils", "Used Size: %s", size_str);

        float usage_percent = (float)sd.GetUsedSize() / sd.GetTotalSize() * 100.0f;
        ESP_LOGI("SDCardUtils", "Usage: %.1f%%", usage_percent);
    }
    ESP_LOGI("SDCardUtils", "=====================");
}

void SDCardUtils::CreateSampleFiles(SDCardManager& sd) {
    if (!sd.IsMounted()) {
        ESP_LOGW("SDCardUtils", "SD card not mounted, cannot create sample files");
        return;
    }

    ESP_LOGI("SDCardUtils", "Creating sample files...");

    if (!sd.FileExists("NIHAO.TXT")) {
        ESP_LOGW("SDCardUtils", "NIHAO.TXT not exists ...");
    } else {
        ESP_LOGI("SDCardUtils", "NIHAO.TXT exists ...");
    }

    char filepath[512];
    sd.GetFullPath("samples/logs/NIHAO.TXT", filepath, sizeof(filepath));
    char filename[512];
    GetFileName(filepath, filename, sizeof(filename));
    ESP_LOGI("SDCardUtils", "Filepath: %s, Filename: %s", filepath, filename);

    // 创建示例目录
    if (!sd.CreateDirectory("samples")) {
        ESP_LOGE("SDCardUtils", "Failed to create samples directory");
        return;
    }

    if (!sd.CreateDirectory("samples/logs")) {
        ESP_LOGW("SDCardUtils", "Failed to create samples/logs directory");
    }

    if (!sd.CreateDirectory("samples/config")) {
        ESP_LOGW("SDCardUtils", "Failed to create samples/config directory");
    }

    if (!sd.CreateDirectory("samples/data")) {
        ESP_LOGW("SDCardUtils", "Failed to create samples/data directory");
    }

    // 创建示例文本文件
    sd.WriteTextFile("samples/readme.txt",
        "This is a sample text file created by SDCardManager.\n"
        "You can read, write, and modify files on the SD card.\n"
        "Enjoy using the SD card functionality!\n");

    // 创建示例JSON配置文件
    sd.WriteJsonFile("samples/config/settings.json",
        "{\n"
        "  \"device_name\": \"XiaoZhi ESP32\",\n"
        "  \"version\": \"1.0.0\",\n"
        "  \"language\": \"zh-CN\",\n"
        "  \"features\": {\n"
        "    \"bluetooth\": true,\n"
        "    \"wifi\": true,\n"
        "    \"sd_card\": true\n"
        "  }\n"
        "}");

	// 写入当前时间戳
	time_t now = time(nullptr);
	char timestamp[64];
	FormatTime(now, timestamp, sizeof(timestamp));

	char test_content[256];
	snprintf(test_content, sizeof(test_content),
		"Touch gesture test\nTimestamp: %s\nGesture: Swipe Down\n", timestamp);

	if (sd.WriteTextFile("samples/readme.txt", test_content)) {
		// 读取文件大小
		size_t file_size = sd.GetFileSize("samples/readme.txt");

		char msg[128];
		snprintf(msg, sizeof(msg), "文件操作成功\n%s\n大小: %d 字节", "samples/readme.txt", file_size);

		ESP_LOGI("SDCardUtils", "File operation successful: %s (%d bytes)", "samples/readme.txt", file_size);
	} else {
		ESP_LOGE("SDCardUtils", "Failed to write test file");
	}

    // 创建示例日志文件
    sd.WriteLog("samples/logs/system.log", "System started", true);
    sd.WriteLog("samples/logs/system.log", "SD card mounted successfully", true);
    sd.WriteLog("samples/logs/system.log", "Sample files created", true);

    // 创建示例数据文件
    uint8_t sample_data[256];
    for (int i = 0; i < 256; i++) {
        sample_data[i] = i;
    }
    sd.WriteFile("samples/data/binary_data.bin", sample_data, sizeof(sample_data));

    ESP_LOGI("SDCardUtils", "✅ Sample files created successfully");
}

void SDCardUtils::TestDirectoryCreation(SDCardManager& sd) {
    if (!sd.IsMounted()) {
        ESP_LOGW("SDCardUtils", "SD card not mounted, cannot test directory creation");
        return;
    }

    ESP_LOGI("SDCardUtils", "=== Testing Directory Creation ===");

    // 测试简单目录创建
    ESP_LOGI("SDCardUtils", "Testing simple directory creation...");
    if (sd.CreateDirectory("test_dir")) {
        ESP_LOGI("SDCardUtils", "✅ Simple directory creation successful");

        // 测试在目录中创建文件
        if (sd.WriteTextFile("test_dir/test.txt", "Test file content")) {
            ESP_LOGI("SDCardUtils", "✅ File creation in directory successful");
        } else {
            ESP_LOGE("SDCardUtils", "❌ File creation in directory failed");
        }
    } else {
        ESP_LOGE("SDCardUtils", "❌ Simple directory creation failed");
    }

    // 测试嵌套目录创建
    ESP_LOGI("SDCardUtils", "Testing nested directory creation...");
    if (sd.CreateDirectory("test_nested/level1/level2")) {
        ESP_LOGI("SDCardUtils", "✅ Nested directory creation successful");

        // 测试在嵌套目录中创建文件
        if (sd.WriteTextFile("test_nested/level1/level2/nested_test.txt", "Nested test content")) {
            ESP_LOGI("SDCardUtils", "✅ File creation in nested directory successful");
        } else {
            ESP_LOGE("SDCardUtils", "❌ File creation in nested directory failed");
        }
    } else {
        ESP_LOGE("SDCardUtils", "❌ Nested directory creation failed");
    }

    ESP_LOGI("SDCardUtils", "=== Directory Creation Test Complete ===");
}

void SDCardUtils::TestRestoredFunctions(SDCardManager& sd) {
    if (!sd.IsMounted()) {
        ESP_LOGW("SDCardUtils", "SD card not mounted, cannot test restored functions");
        return;
    }

    ESP_LOGI("SDCardUtils", "=== Testing Restored SD Card Functions ===");

    // 测试目录存在检查
    ESP_LOGI("SDCardUtils", "Testing DirectoryExists...");
    if (sd.DirectoryExists("samples")) {
        ESP_LOGI("SDCardUtils", "✅ DirectoryExists works correctly");
    } else {
        ESP_LOGW("SDCardUtils", "⚠️ DirectoryExists returned false for existing directory");
    }

    // 测试目录列表功能
    ESP_LOGI("SDCardUtils", "Testing ListDirectory...");
    SDCardManager::FileInfo files[10];
    size_t file_count = 0;

    if (sd.ListDirectory("samples", files, 10, &file_count)) {
        ESP_LOGI("SDCardUtils", "✅ ListDirectory works correctly, found %d items:", file_count);
        for (size_t i = 0; i < file_count; i++) {
            ESP_LOGI("SDCardUtils", "  - %s (%s, %d bytes)",
                     files[i].name,
                     files[i].is_directory ? "DIR" : "FILE",
                     files[i].size);
        }
    } else {
        ESP_LOGW("SDCardUtils", "⚠️ ListDirectory failed");
    }

    // 测试日志写入功能（带时间戳）
    ESP_LOGI("SDCardUtils", "Testing WriteLog with timestamp...");
    if (sd.WriteLog("samples/logs/test_restored.log", "Testing restored log functionality", true)) {
        ESP_LOGI("SDCardUtils", "✅ WriteLog with timestamp works correctly");
    } else {
        ESP_LOGW("SDCardUtils", "⚠️ WriteLog with timestamp failed");
    }

    // 测试目录删除功能（创建临时目录进行测试）
    ESP_LOGI("SDCardUtils", "Testing DeleteDirectory...");
    const char* test_dir = "samples/temp_test_dir";

    if (sd.CreateDirectory(test_dir)) {
        // 在测试目录中创建一些文件
        sd.WriteTextFile("samples/temp_test_dir/test1.txt", "Test file 1");
        sd.WriteTextFile("samples/temp_test_dir/test2.txt", "Test file 2");
        sd.CreateDirectory("samples/temp_test_dir/subdir");
        sd.WriteTextFile("samples/temp_test_dir/subdir/test3.txt", "Test file 3");

        // 尝试删除目录
        if (sd.DeleteDirectory(test_dir)) {
            ESP_LOGI("SDCardUtils", "✅ DeleteDirectory works correctly");
        } else {
            ESP_LOGW("SDCardUtils", "⚠️ DeleteDirectory failed");
        }
    } else {
        ESP_LOGW("SDCardUtils", "⚠️ Could not create test directory for DeleteDirectory test");
    }

    // 测试时间格式化功能
    ESP_LOGI("SDCardUtils", "Testing FormatTime...");
    char time_buffer[64];
    time_t current_time = time(nullptr);
    FormatTime(current_time, time_buffer, sizeof(time_buffer));
    ESP_LOGI("SDCardUtils", "✅ FormatTime result: %s", time_buffer);

    ESP_LOGI("SDCardUtils", "=== Restored Functions Test Complete ===");
}

void SDCardUtils::TestPinConflictResolution() {
    ESP_LOGI("SDCardUtils", "=== Testing Pin Conflict Resolution ===");

    // 显示当前的引脚分配
    ESP_LOGI("SDCardUtils", "Current pin assignments:");
    ESP_LOGI("SDCardUtils", "LCD pins (SH8601):");
    ESP_LOGI("SDCardUtils", "  CS:    GPIO_12");
    ESP_LOGI("SDCardUtils", "  PCLK:  GPIO_38");
    ESP_LOGI("SDCardUtils", "  DATA0: GPIO_4");
    ESP_LOGI("SDCardUtils", "  DATA1: GPIO_5");
    ESP_LOGI("SDCardUtils", "  DATA2: GPIO_6");
    ESP_LOGI("SDCardUtils", "  DATA3: GPIO_7");
    ESP_LOGI("SDCardUtils", "  RST:   GPIO_39");

    ESP_LOGI("SDCardUtils", "Touch pins (CST9217):");
    ESP_LOGI("SDCardUtils", "  SDA:   GPIO_15");
    ESP_LOGI("SDCardUtils", "  SCL:   GPIO_14");
    ESP_LOGI("SDCardUtils", "  RST:   GPIO_40");

    ESP_LOGI("SDCardUtils", "SDMMC pins (configured):");
    ESP_LOGI("SDCardUtils", "  CLK:   GPIO_2");
    ESP_LOGI("SDCardUtils", "  CMD:   GPIO_1");
    ESP_LOGI("SDCardUtils", "  D0:    GPIO_3");
    ESP_LOGI("SDCardUtils", "  D1-D7: NC (disabled)");

    // 验证没有冲突
    const int lcd_pins[] = {4, 5, 6, 7, 12, 38, 39};
    const int touch_pins[] = {14, 15, 40};
    const int sdmmc_pins[] = {1, 2, 3};

    bool conflict_found = false;

    // 检查SDMMC与LCD的冲突
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 7; j++) {
            if (sdmmc_pins[i] == lcd_pins[j]) {
                ESP_LOGE("SDCardUtils", "❌ CONFLICT: SDMMC GPIO_%d conflicts with LCD!", sdmmc_pins[i]);
                conflict_found = true;
            }
        }
    }

    // 检查SDMMC与Touch的冲突
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            if (sdmmc_pins[i] == touch_pins[j]) {
                ESP_LOGE("SDCardUtils", "❌ CONFLICT: SDMMC GPIO_%d conflicts with Touch!", sdmmc_pins[i]);
                conflict_found = true;
            }
        }
    }

    // 检查LCD与Touch的冲突
    for (int i = 0; i < 7; i++) {
        for (int j = 0; j < 3; j++) {
            if (lcd_pins[i] == touch_pins[j]) {
                ESP_LOGE("SDCardUtils", "❌ CONFLICT: LCD GPIO_%d conflicts with Touch!", lcd_pins[i]);
                conflict_found = true;
            }
        }
    }

    if (!conflict_found) {
        ESP_LOGI("SDCardUtils", "✅ Pin conflict resolution successful!");
        ESP_LOGI("SDCardUtils", "✅ All peripherals use separate GPIO pins");
        ESP_LOGI("SDCardUtils", "✅ SDMMC, LCD, and Touch can work simultaneously");
    } else {
        ESP_LOGE("SDCardUtils", "❌ Pin conflicts still exist!");
    }

    ESP_LOGI("SDCardUtils", "=== Pin Conflict Test Complete ===");
    //gpio_dump_io_configuration(stdout, SOC_GPIO_VALID_GPIO_MASK);
}
