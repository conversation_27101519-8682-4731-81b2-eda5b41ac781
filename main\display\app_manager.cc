#include "app_manager.h"
#include <esp_log.h>
#include <algorithm>

const char* AppManager::TAG = "AppManager";

// ============================================================================
// BaseApp 基类实现
// ============================================================================

const char* BaseApp::TAG = "BaseApp";

BaseApp::BaseApp(AppType type, const char* name, const char* icon)
    : type_(type)
    , name_(name)
    , icon_(icon)
    , state_(AppState::UNINSTALLED)
    , container_(nullptr)
    , is_visible_(false)
    , install_time_(0)
    , start_time_(0)
    , parent_(nullptr)
{
    ESP_LOGI(TAG, "Creating app: %s", name_);
}

BaseApp::~BaseApp() {
    if (container_) {
        lv_obj_del(container_);
        container_ = nullptr;
    }
    parent_ = nullptr;
    ESP_LOGI(TAG, "Destroying app: %s", name_);
}

bool BaseApp::OnEvent(AppEvent event, void* data) {
    ESP_LOGD(TAG, "App %s received event: %s", name_, GetAppEventName(event));
    
    switch (event) {
        case AppEvent::INSTALL:
            return OnInstall();
        case AppEvent::UNINSTALL:
            return OnUninstall();
        case AppEvent::START:
            return OnStart();
        case AppEvent::STOP:
            return OnStop();
        case AppEvent::PAUSE:
            return OnPause();
        case AppEvent::RESUME:
            return OnResume();
        case AppEvent::GESTURE_SWIPE_LEFT:
        case AppEvent::GESTURE_SWIPE_RIGHT:
        case AppEvent::GESTURE_SWIPE_UP:
        case AppEvent::GESTURE_SWIPE_DOWN:
        case AppEvent::GESTURE_TAP:
        case AppEvent::GESTURE_LONG_PRESS:
            // 默认不处理手势，返回false让AppManager处理
            return false;
        default:
            return false;
    }
}

void BaseApp::SetState(AppState new_state) {
    if (state_ != new_state) {
        AppState old_state = state_;
        state_ = new_state;
        ESP_LOGI(TAG, "App %s state changed: %s -> %s", 
                name_, GetAppStateName(old_state), GetAppStateName(new_state));
        
        // 记录时间戳
        uint64_t current_time = esp_timer_get_time();
        if (new_state == AppState::INSTALLED) {
            install_time_ = current_time;
        } else if (new_state == AppState::RUNNING) {
            start_time_ = current_time;
        }
    }
}

// ============================================================================
// AppManager 实现
// ============================================================================

AppManager::AppManager(lv_obj_t* parent)
    : parent_(parent)
    , current_app_index_(-1)
    , is_transitioning_(false)
{
    ESP_LOGI(TAG, "App manager initialized");
}

AppManager::~AppManager() {
    // 停止所有运行中的App
    for (auto& app : apps_) {
        if (app->GetState() == AppState::RUNNING) {
            app->OnEvent(AppEvent::STOP);
        }
        if (app->GetState() != AppState::UNINSTALLED) {
            app->OnEvent(AppEvent::UNINSTALL);
        }
    }
    apps_.clear();
    parent_ = nullptr;
    ESP_LOGI(TAG, "App manager destroyed");
}

bool AppManager::InstallApp(std::unique_ptr<BaseApp> app) {
    if (!app) {
        ESP_LOGE(TAG, "Cannot install null app");
        return false;
    }

    ESP_LOGI(TAG, "Installing app: %s", app->GetName());
    app->SetParent(parent_);

    // 安装App
    if (!app->OnEvent(AppEvent::INSTALL)) {
        ESP_LOGE(TAG, "Failed to install app: %s", app->GetName());
        return false;
    }

    // 设置id
    app->SetId(apps_.size());
    // 追加
    apps_.push_back(std::move(app));
    // 设置id
    // apps_[apps_.size() - 1]->SetId(apps_.size() - 1);
    
    // 如果是第一个App，自动启动
    if (apps_.size() == 1) {
        StartApp(0, AppTransition::NONE);
    }

    ESP_LOGI(TAG, "App installed successfully: %s", apps_.back()->GetName());
    return true;
}

bool AppManager::UninstallApp(BaseApp& app) {
    int app_index = app.GetId();
    if (app_index == -1) {
        ESP_LOGW(TAG, "App not found for uninstall: %d", app_index);
        return false;
    }

    ESP_LOGI(TAG, "Uninstalling app: %s", app.GetName());

    // 如果是当前运行的App，先停止
    if (app_index == current_app_index_) {
        StopApp(app.GetType());
        // 切换到下一个可用的App
        if (apps_.size() > 1) {
            int next_index = (app_index + 1) % apps_.size();
            if (next_index == app_index) {
                next_index = (app_index - 1 + apps_.size()) % apps_.size();
            }
            StartApp(next_index);
        } else {
            current_app_index_ = -1;
        }
    }

    // 卸载App
    app.OnEvent(AppEvent::UNINSTALL);
    
    // 从列表中移除
    apps_.erase(apps_.begin() + app_index);
    
    // 更新当前App索引
    if (current_app_index_ > app_index) {
        current_app_index_--;
    } else if (current_app_index_ == app_index) {
        current_app_index_ = -1;
    }

    ESP_LOGI(TAG, "App uninstalled successfully");
    return true;
}

bool AppManager::StartApp(int target_index, AppTransition transition) {
    if (is_transitioning_) {
        ESP_LOGW(TAG, "App transition in progress, ignoring start request");
        return false;
    }

    if (target_index == current_app_index_) {
        ESP_LOGD(TAG, "App already running: %s", apps_[target_index]->GetName());
        return true;
    }

    //AppType type = apps_[0]->GetType();

    BaseApp* from_app = GetCurrentApp();
    BaseApp* to_app = GetApp(target_index);
    if (to_app == nullptr) return false;

    ESP_LOGI(TAG, "Starting app: %s", to_app->GetName());

    // 触发App切换回调
    if (on_app_changed_ && from_app) {
        on_app_changed_(from_app->GetType(), to_app->GetType());
    }

    // 停止当前App
    if (from_app && from_app->GetState() == AppState::RUNNING) {
        from_app->OnEvent(AppEvent::STOP);
    }

    // 启动目标App
    if (!to_app->OnEvent(AppEvent::START)) {
        ESP_LOGE(TAG, "Failed to start app: %s", to_app->GetName());
        return false;
    }

    // 创建切换动画
    CreateAppTransition(from_app, to_app, transition);
    
    current_app_index_ = target_index;
    return true;
}

bool AppManager::StopApp(AppType type) {
    int app_index = FindAppIndex(type);
    if (app_index == -1) {
        ESP_LOGW(TAG, "App not found for stop: %d", static_cast<int>(type));
        return false;
    }

    BaseApp* app = apps_[app_index].get();
    if (app->GetState() != AppState::RUNNING) {
        ESP_LOGD(TAG, "App not running: %s", app->GetName());
        return true;
    }

    ESP_LOGI(TAG, "Stopping app: %s", app->GetName());
    return app->OnEvent(AppEvent::STOP);
}

bool AppManager::NavigateToNext(AppTransition transition) {
    if (apps_.empty()) return false;
    
    int next_index = (current_app_index_ + 1) % apps_.size();
    return StartApp(next_index, transition);
}

bool AppManager::NavigateToPrevious(AppTransition transition) {
    if (apps_.empty()) return false;
    
    int prev_index = (current_app_index_ - 1 + apps_.size()) % apps_.size();
    return StartApp(prev_index, transition);
}

bool AppManager::NavigateToApp(AppType type, AppTransition transition) {
    int index = FindAppIndex(type);
    if (index == -1) {
        ESP_LOGE(TAG, "App not found for navigation: %d", static_cast<int>(type));
        return false;
    }
    return StartApp(index, transition);
}

void AppManager::HandleGesture(AppEvent gesture_event) {
    BaseApp* current = GetCurrentApp();
    if (current && current->OnEvent(gesture_event)) {
        return; // App已处理手势
    }
    
    // 默认手势处理
    switch (gesture_event) {
        case AppEvent::GESTURE_SWIPE_LEFT:
            NavigateToNext(AppTransition::SLIDE_LEFT);
            break;
        case AppEvent::GESTURE_SWIPE_RIGHT:
            NavigateToPrevious(AppTransition::SLIDE_RIGHT);
            break;
        default:
            // 其他手势由当前App处理
            break;
    }
}

BaseApp* AppManager::GetCurrentApp() const {
    if (current_app_index_ >= 0 && current_app_index_ < apps_.size()) {
        return apps_[current_app_index_].get();
    }
    return nullptr;
}

BaseApp* AppManager::GetApp(AppType type) const {
    int index = FindAppIndex(type);
    if (index >= 0) {
        return apps_[index].get();
    }
    return nullptr;
}

BaseApp* AppManager::GetApp(int target_index) const {
    if (target_index < 0 || target_index >= apps_.size()) {
        ESP_LOGE(TAG, "App not found for start: %d", target_index);
        return nullptr;
    }
    return apps_[target_index].get();
}

AppType AppManager::GetCurrentAppType() const {
    BaseApp* current = GetCurrentApp();
    return current ? current->GetType() : AppType::MAIN_CHAT;
}

void AppManager::SetOnAppChanged(std::function<void(AppType, AppType)> callback) {
    on_app_changed_ = callback;
}

void AppManager::Update() {
    BaseApp* current = GetCurrentApp();
    if (current && current->GetState() == AppState::RUNNING) {
        current->OnUpdate();
    }
}

std::vector<AppType> AppManager::GetInstalledApps() const {
    std::vector<AppType> installed_apps;
    for (const auto& app : apps_) {
        if (app->GetState() != AppState::UNINSTALLED) {
            installed_apps.push_back(app->GetType());
        }
    }
    return installed_apps;
}

void AppManager::CreateAppTransition(BaseApp* from_app, BaseApp* to_app, AppTransition transition) {
    if (!to_app) return;

    is_transitioning_ = true;

    // 隐藏当前App
    if (from_app && from_app->IsVisible()) {
        from_app->SetVisible(false);
        if (from_app->GetContainer()) {
            lv_obj_add_flag(from_app->GetContainer(), LV_OBJ_FLAG_HIDDEN);
        }
    }

    // 显示目标App
    to_app->SetVisible(true);
    if (to_app->GetContainer()) {
        lv_obj_clear_flag(to_app->GetContainer(), LV_OBJ_FLAG_HIDDEN);
    }

    // 完成切换
    OnTransitionComplete(from_app, to_app);
}

void AppManager::OnTransitionComplete(BaseApp* from_app, BaseApp* to_app) {
    is_transitioning_ = false;
    ESP_LOGI(TAG, "App transition completed to: %s", to_app->GetName());
}

int AppManager::FindAppIndex(AppType type) const {
    for (size_t i = 0; i < apps_.size(); ++i) {
        if (apps_[i]->GetType() == type) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

// ============================================================================
// 全局函数
// ============================================================================

const char* GetAppTypeName(AppType type) {
    switch (type) {
        case AppType::MAIN_CHAT: return "Main Chat";
        case AppType::SETTINGS: return "Settings";
        case AppType::SYSTEM_INFO: return "System Info";
        case AppType::MEDIA_PLAYER: return "Media Player";
        case AppType::FILE_BROWSER: return "File Browser";
        default: return "Unknown";
    }
}

const char* GetAppStateName(AppState state) {
    switch (state) {
        case AppState::UNINSTALLED: return "Uninstalled";
        case AppState::INSTALLED: return "Installed";
        case AppState::RUNNING: return "Running";
        case AppState::PAUSED: return "Paused";
        case AppState::STOPPED: return "Stopped";
        default: return "Unknown";
    }
}

const char* GetAppEventName(AppEvent event) {
    switch (event) {
        case AppEvent::INSTALL: return "Install";
        case AppEvent::UNINSTALL: return "Uninstall";
        case AppEvent::START: return "Start";
        case AppEvent::STOP: return "Stop";
        case AppEvent::PAUSE: return "Pause";
        case AppEvent::RESUME: return "Resume";
        case AppEvent::GESTURE_SWIPE_LEFT: return "Swipe Left";
        case AppEvent::GESTURE_SWIPE_RIGHT: return "Swipe Right";
        case AppEvent::GESTURE_SWIPE_UP: return "Swipe Up";
        case AppEvent::GESTURE_SWIPE_DOWN: return "Swipe Down";
        case AppEvent::GESTURE_TAP: return "Tap";
        case AppEvent::GESTURE_LONG_PRESS: return "Long Press";
        default: return "Unknown";
    }
}

// ============================================================================
// SettingsApp 实现
// ============================================================================

const char* SettingsApp::TAG = "SettingsApp";

SettingsApp::SettingsApp()
    : BaseApp(AppType::SETTINGS, "Settings", "⚙️")
    , settings_list_(nullptr)
{
}

bool SettingsApp::OnInstall() {
    ESP_LOGI(TAG, "Installing settings app");
    SetState(AppState::INSTALLED);
    return true;
}

bool SettingsApp::OnUninstall() {
    ESP_LOGI(TAG, "Uninstalling settings app");
    if (container_) {
        lv_obj_del(container_);
        container_ = nullptr;
    }
    settings_list_ = nullptr;
    SetState(AppState::UNINSTALLED);
    return true;
}

bool SettingsApp::OnStart() {
    ESP_LOGI(TAG, "Starting settings app");

    if (!container_) {
        // 创建主容器
        container_ = lv_obj_create(parent_);
        lv_obj_set_size(container_, LV_HOR_RES, LV_VER_RES);
        lv_obj_set_style_pad_all(container_, 10, 0);
        lv_obj_set_flex_flow(container_, LV_FLEX_FLOW_COLUMN);

        // 创建标题
        lv_obj_t* title = lv_label_create(container_);
        lv_label_set_text(title, "⚙️ 设置");
        lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
        lv_obj_set_style_text_align(title, LV_TEXT_ALIGN_CENTER, 0);

        // 创建设置列表
        settings_list_ = lv_obj_create(container_);
        lv_obj_set_size(settings_list_, LV_HOR_RES - 20, LV_VER_RES - 60);
        lv_obj_set_flex_flow(settings_list_, LV_FLEX_FLOW_COLUMN);
        lv_obj_set_style_pad_all(settings_list_, 5, 0);

        CreateSettingsItems();
    }

    SetVisible(true);
    lv_obj_clear_flag(container_, LV_OBJ_FLAG_HIDDEN);
    SetState(AppState::RUNNING);
    return true;
}

bool SettingsApp::OnStop() {
    ESP_LOGI(TAG, "Stopping settings app");
    if (container_) {
        lv_obj_add_flag(container_, LV_OBJ_FLAG_HIDDEN);
    }
    SetVisible(false);
    SetState(AppState::STOPPED);
    return true;
}

bool SettingsApp::OnEvent(AppEvent event, void* data) {
    // 先调用基类处理
    if (BaseApp::OnEvent(event, data)) {
        return true;
    }

    // 处理特定手势
    switch (event) {
        case AppEvent::GESTURE_TAP:
            ESP_LOGI(TAG, "Settings app received tap");
            return true; // 处理了事件
        default:
            return false; // 未处理，让AppManager处理
    }
}

void SettingsApp::CreateSettingsItems() {
    if (!settings_list_) return;

    // WiFi设置
    lv_obj_t* wifi_item = lv_label_create(settings_list_);
    lv_label_set_text(wifi_item, "📶 WiFi配置");
    lv_obj_set_style_pad_ver(wifi_item, 5, 0);

    // 音量设置
    lv_obj_t* volume_item = lv_label_create(settings_list_);
    lv_label_set_text(volume_item, "🔊 音量调节");
    lv_obj_set_style_pad_ver(volume_item, 5, 0);

    // 显示设置
    lv_obj_t* display_item = lv_label_create(settings_list_);
    lv_label_set_text(display_item, "🖥️ 显示设置");
    lv_obj_set_style_pad_ver(display_item, 5, 0);

    // 系统信息
    lv_obj_t* system_item = lv_label_create(settings_list_);
    lv_label_set_text(system_item, "ℹ️ 系统信息");
    lv_obj_set_style_pad_ver(system_item, 5, 0);

    // 提示信息
    lv_obj_t* hint = lv_label_create(settings_list_);
    lv_label_set_text(hint, "\n左右滑动切换App");
    lv_obj_set_style_text_align(hint, LV_TEXT_ALIGN_CENTER, 0);
    lv_obj_set_style_text_color(hint, lv_color_hex(0x888888), 0);
}

// ============================================================================
// SystemInfoApp 实现
// ============================================================================

const char* SystemInfoApp::TAG = "SystemInfoApp";

SystemInfoApp::SystemInfoApp()
    : BaseApp(AppType::SYSTEM_INFO, "System Info", "📊")
    , info_list_(nullptr)
{
}

bool SystemInfoApp::OnInstall() {
    ESP_LOGI(TAG, "Installing system info app");
    SetState(AppState::INSTALLED);
    return true;
}

bool SystemInfoApp::OnUninstall() {
    ESP_LOGI(TAG, "Uninstalling system info app");
    if (container_) {
        lv_obj_del(container_);
        container_ = nullptr;
    }
    info_list_ = nullptr;
    SetState(AppState::UNINSTALLED);
    return true;
}

bool SystemInfoApp::OnStart() {
    ESP_LOGI(TAG, "Starting system info app");

    if (!container_) {
        // 创建主容器
        container_ = lv_obj_create(parent_);
        lv_obj_set_size(container_, LV_HOR_RES, LV_VER_RES);
        lv_obj_set_style_pad_all(container_, 10, 0);
        lv_obj_set_flex_flow(container_, LV_FLEX_FLOW_COLUMN);

        // 创建标题
        lv_obj_t* title = lv_label_create(container_);
        lv_label_set_text(title, "📊 系统信息");
        lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
        lv_obj_set_style_text_align(title, LV_TEXT_ALIGN_CENTER, 0);

        // 创建信息列表
        info_list_ = lv_obj_create(container_);
        lv_obj_set_size(info_list_, LV_HOR_RES - 20, LV_VER_RES - 60);
        lv_obj_set_flex_flow(info_list_, LV_FLEX_FLOW_COLUMN);
        lv_obj_set_style_pad_all(info_list_, 5, 0);

        UpdateSystemInfo();
    }

    SetVisible(true);
    lv_obj_clear_flag(container_, LV_OBJ_FLAG_HIDDEN);
    SetState(AppState::RUNNING);
    return true;
}

bool SystemInfoApp::OnStop() {
    ESP_LOGI(TAG, "Stopping system info app");
    if (container_) {
        lv_obj_add_flag(container_, LV_OBJ_FLAG_HIDDEN);
    }
    SetVisible(false);
    SetState(AppState::STOPPED);
    return true;
}

void SystemInfoApp::OnUpdate() {
    UpdateSystemInfo();
}

bool SystemInfoApp::OnEvent(AppEvent event, void* data) {
    // 先调用基类处理
    if (BaseApp::OnEvent(event, data)) {
        return true;
    }

    // 处理特定手势
    switch (event) {
        case AppEvent::GESTURE_TAP:
            ESP_LOGI(TAG, "System info app received tap");
            UpdateSystemInfo(); // 点击时刷新信息
            return true; // 处理了事件
        default:
            return false; // 未处理，让AppManager处理
    }
}

void SystemInfoApp::UpdateSystemInfo() {
    if (!info_list_) return;

    // 清除现有内容
    lv_obj_clean(info_list_);

    // CPU信息
    lv_obj_t* cpu_info = lv_label_create(info_list_);
    lv_label_set_text(cpu_info, "💻 CPU: ESP32-S3");
    lv_obj_set_style_pad_ver(cpu_info, 3, 0);

    // 内存信息
    lv_obj_t* mem_info = lv_label_create(info_list_);
    char mem_text[64];
    snprintf(mem_text, sizeof(mem_text), "🧠 内存: %ld KB", esp_get_free_heap_size() / 1024);
    lv_label_set_text(mem_info, mem_text);
    lv_obj_set_style_pad_ver(mem_info, 3, 0);

    // 运行时间
    lv_obj_t* uptime_info = lv_label_create(info_list_);
    char uptime_text[64];
    uint64_t uptime_ms = esp_timer_get_time() / 1000;
    snprintf(uptime_text, sizeof(uptime_text), "⏱️ 运行: %ld 秒", (long)uptime_ms / 1000);
    lv_label_set_text(uptime_info, uptime_text);
    lv_obj_set_style_pad_ver(uptime_info, 3, 0);

    // App状态信息
    lv_obj_t* app_info = lv_label_create(info_list_);
    char app_text[64];
    snprintf(app_text, sizeof(app_text), "📱 App状态: %s", GetAppStateName(GetState()));
    lv_label_set_text(app_info, app_text);
    lv_obj_set_style_pad_ver(app_info, 3, 0);

    // 提示信息
    lv_obj_t* hint = lv_label_create(info_list_);
    lv_label_set_text(hint, "\n左右滑动切换App\n点击刷新信息");
    lv_obj_set_style_text_align(hint, LV_TEXT_ALIGN_CENTER, 0);
    lv_obj_set_style_text_color(hint, lv_color_hex(0x888888), 0);
}
