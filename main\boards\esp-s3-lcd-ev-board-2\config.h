#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>


#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_5
#define AUDIO_I2S_GPIO_WS   GPIO_NUM_7
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_16
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_15
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_6

#define BSP_POWER_AMP_IO         (IO_EXPANDER_PIN_NUM_0)
#define AUDIO_CODEC_PA_PIN       GPIO_NUM_NC

#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_47
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_48

#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR  0x82


#define BUILTIN_LED_GPIO        GPIO_NUM_4
#define BOOT_BUTTON_GPIO        GPIO_NUM_0

#define DISPLAY_WIDTH     800
#define DISPLAY_HEIGHT    480
#define DISPLAY_MIRROR_X  false
#define DISPLAY_MIRROR_Y  false
#define DISPLAY_SWAP_XY   false

#define DISPLAY_OFFSET_X  0
#define DISPLAY_OFFSET_Y  0

#define DISPLAY_BACKLIGHT_PIN            GPIO_NUM_19
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT  false


#endif // _BOARD_CONFIG_H_
