# 深度睡眠立即唤醒问题解决方案

## 问题描述

使用PowerSaveButton后，设备进入深度睡眠模式立即被唤醒，无法正常进入省电状态。

## 问题原因分析

### 1. GPIO0的特殊性
- **GPIO0是ESP32-S3的启动模式控制引脚**
- 启动时如果GPIO0被拉低，芯片进入下载模式
- PowerSaveButton启用省电功能后，GPIO0状态可能不稳定

### 2. PowerSaveButton的配置
```cpp
// PowerSaveButton的默认配置
PowerSaveButton(gpio_num_t gpio_num) : Button(gpio_num, false, 0, 0, true)
//                                                              ^^^^
//                                                        enable_power_save = true
```

### 3. 唤醒条件冲突
- PowerSaveButton自动启用GPIO唤醒功能
- GPIO0状态不稳定导致误触发唤醒条件
- 设备刚进入睡眠就被立即唤醒

## 解决方案

### 方案1：禁用PowerSaveButton的省电功能（推荐）

**修改前**：
```cpp
PowerSaveButton boot_button_;
// 构造函数：boot_button_(BOOT_BUTTON_GPIO)
```

**修改后**：
```cpp
Button boot_button_;
// 构造函数：boot_button_(BOOT_BUTTON_GPIO, false, 2000, 0, false)
//                                          ^^^^^  ^^^^  ^  ^^^^^
//                                          |      |     |  禁用省电功能
//                                          |      |     短按时间
//                                          |      长按时间(2秒)
//                                          低电平触发
```

### 方案2：正确配置GPIO唤醒（如需唤醒功能）

如果您需要按钮唤醒功能，使用以下配置：

```cpp
void ConfigureWakeupSource() {
    // 清除之前的唤醒源
    esp_sleep_disable_wakeup_source(ESP_SLEEP_WAKEUP_ALL);
    
    // 配置GPIO唤醒 - 低电平触发
    const uint64_t ext_wakeup_pin_1_mask = 1ULL << BOOT_BUTTON_GPIO;
    esp_err_t ret = esp_sleep_enable_ext1_wakeup(ext_wakeup_pin_1_mask, ESP_EXT1_WAKEUP_ALL_LOW);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure GPIO wakeup: %s", esp_err_to_name(ret));
        return;
    }
    
    // 配置GPIO为输入模式，启用内部上拉
    gpio_config_t config = {
        .pin_bit_mask = ext_wakeup_pin_1_mask,
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_ENABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE
    };
    gpio_config(&config);
    
    // 等待GPIO状态稳定
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 检查当前GPIO状态，避免误触发
    int gpio_level = gpio_get_level(BOOT_BUTTON_GPIO);
    if (gpio_level == 0) {
        ESP_LOGW(TAG, "Boot button is pressed, skipping sleep mode");
        return;
    }
    
    ESP_LOGI(TAG, "Wakeup source configured, GPIO%d level: %d", BOOT_BUTTON_GPIO, gpio_level);
}
```

### 方案3：使用其他GPIO作为唤醒源

如果GPIO0问题持续存在，考虑使用其他GPIO：

```cpp
// 使用触摸中断引脚作为唤醒源（如果有的话）
#define WAKEUP_GPIO GPIO_NUM_40  // 触摸中断引脚

// 在PowerSaveTimer的OnEnterSleepMode中配置
power_save_timer_->OnEnterSleepMode([this]() {
    GetDisplay()->SetPowerSaveMode(true);
    GetBacklight()->SetBrightness(20);
    
    // 配置触摸唤醒
    const uint64_t wakeup_pin_mask = 1ULL << WAKEUP_GPIO;
    esp_sleep_enable_ext1_wakeup(wakeup_pin_mask, ESP_EXT1_WAKEUP_ALL_LOW);
});
```

## 实施步骤

### 1. 修改按钮定义
```cpp
// 在类定义中
class WaveshareEsp32s3TouchAMOLED1inch75 : public WifiBoard {
private:
    Button boot_button_;  // 改为普通Button
    // ...
};
```

### 2. 修改构造函数
```cpp
// 在构造函数中
WaveshareEsp32s3TouchAMOLED1inch75() : 
    boot_button_(BOOT_BUTTON_GPIO, false, 2000, 0, false) {  // 禁用省电功能
    // ...
}
```

### 3. 添加必要的头文件
```cpp
#include <esp_sleep.h>
#include <driver/gpio.h>
```

### 4. 配置唤醒源（可选）
```cpp
void InitializePowerSaveTimer() {
    power_save_timer_ = new PowerSaveTimer(-1, 60, 300);
    power_save_timer_->OnEnterSleepMode([this]() {
        GetDisplay()->SetPowerSaveMode(true);
        GetBacklight()->SetBrightness(20);
        ConfigureWakeupSource();  // 添加唤醒配置
    });
    // ...
}
```

## 调试方法

### 1. 检查GPIO状态
```cpp
// 在进入睡眠前检查GPIO状态
int gpio_level = gpio_get_level(BOOT_BUTTON_GPIO);
ESP_LOGI(TAG, "GPIO%d level before sleep: %d", BOOT_BUTTON_GPIO, gpio_level);
```

### 2. 检查唤醒原因
```cpp
// 在启动时检查唤醒原因
esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();
switch (wakeup_reason) {
    case ESP_SLEEP_WAKEUP_EXT1:
        ESP_LOGI(TAG, "Wakeup caused by external signal using RTC_CNTL");
        break;
    case ESP_SLEEP_WAKEUP_TIMER:
        ESP_LOGI(TAG, "Wakeup caused by timer");
        break;
    case ESP_SLEEP_WAKEUP_UNDEFINED:
    default:
        ESP_LOGI(TAG, "Not a deep sleep reset");
        break;
}
```

### 3. 监控睡眠时间
```cpp
// 记录进入睡眠的时间
static int64_t sleep_enter_time = 0;

power_save_timer_->OnEnterSleepMode([this]() {
    sleep_enter_time = esp_timer_get_time();
    ESP_LOGI(TAG, "Entering sleep mode at: %lld", sleep_enter_time);
    // ...
});

// 在唤醒后检查睡眠时长
int64_t sleep_duration = esp_timer_get_time() - sleep_enter_time;
ESP_LOGI(TAG, "Sleep duration: %lld microseconds", sleep_duration);
```

## 预期效果

实施解决方案后：

1. **设备能正常进入深度睡眠**
2. **不会立即被唤醒**
3. **按钮功能正常工作**
4. **省电效果明显**

## 常见问题

### Q: 修改后按钮还能唤醒设备吗？
A: 如果使用方案1（禁用省电功能），按钮无法唤醒设备。如果需要唤醒功能，请使用方案2。

### Q: 为什么不直接修复PowerSaveButton？
A: GPIO0在ESP32-S3上有特殊用途，最好避免在这个引脚上使用复杂的省电功能。

### Q: 其他开发板会有同样问题吗？
A: 主要影响使用GPIO0作为按钮且启用PowerSaveButton的开发板。

## 总结

GPIO0的特殊性导致PowerSaveButton在某些情况下会引起深度睡眠立即唤醒的问题。通过禁用省电功能或正确配置唤醒源，可以有效解决这个问题。推荐使用方案1（禁用省电功能）作为最简单可靠的解决方案。
