#pragma once

#include <lvgl.h>
#include <functional>
#include <esp_log.h>
#include <sys/time.h>
#include <esp_timer.h>

/**
 * @brief 触摸手势类型枚举
 */
enum class TouchGesture {
    NONE,           // 无手势
    SWIPE_LEFT,     // 向左滑动
    SWIPE_RIGHT,    // 向右滑动
    SWIPE_UP,       // 向上滑动
    SWIPE_DOWN,     // 向下滑动
    TAP,            // 点击
    LONG_PRESS,     // 长按
    PINCH_IN,       // 缩小手势
    PINCH_OUT,      // 放大手势
	MAX             // 最后
};

/**
 * @brief 触摸点信息结构
 */
struct TouchPoint {
    int32_t x;
    int32_t y;
    bool pressed;
    uint32_t timestamp;
};

/**
 * @brief 触摸手势处理器类
 */
class TouchGestureHandler {
public:
    /**
     * @brief 构造函数
     * @param indev LVGL输入设备对象
     */
    TouchGestureHandler(lv_indev_t* indev);

    /**
     * @brief 析构函数
     */
    ~TouchGestureHandler();

    // 启用/禁用触摸手势处理
    void Enabled(bool enabled);
    // 是否启用
    bool Enabled() const { return enabled_; }

    /**
     * @brief 设置手势回调函数 - left
     */
    void OnSwipeLeft(std::function<void()> callback);
    /**
     * @brief 设置手势回调函数 - right
     */
    void OnSwipeRight(std::function<void()> callback);
    /**
     * @brief 设置手势回调函数 - up
     */
    void OnSwipeUp(std::function<void()> callback);
    /**
     * @brief 设置手势回调函数 - down
     */
    void OnSwipeDown(std::function<void()> callback);
    /**
     * @brief 设置手势回调函数 - Tap
     */
    void OnTap(std::function<void()> callback);
    /**
     * @brief 设置手势回调函数 - longPress
     */
    void OnLongPress(std::function<void()> callback);

    /**
     * @brief 设置滑动阈值
     * @param threshold 滑动距离阈值（像素）
     */
    void SetSwipeThreshold(int16_t threshold) { swipe_threshold_ = threshold; }

    /**
     * @brief 设置长按时间阈值
     * @param time_ms 长按时间阈值（毫秒）
     */
    void SetLongPressTime(uint32_t time_ms) { long_press_time_ = time_ms; }

    /**
     * @brief 启用/禁用特定手势
     * @param gesture 手势类型
     * @param enabled 是否启用
     */
    void SetGestureEnabled(TouchGesture gesture, bool enabled);

    /**
     * @brief 检查手势是否启用
     * @param gesture 手势类型
     * @return 是否启用
     */
    bool IsGestureEnabled(TouchGesture gesture) const;

private:
    /**
     * @brief 手势回调函数 - left
     */
    std::function<void()> on_swipe_left_;
    /**
     * @brief 手势回调函数 - right
     */
    std::function<void()> on_swipe_right_;
    /**
     * @brief 手势回调函数 - up
     */
    std::function<void()> on_swipe_up_;
    /**
     * @brief 手势回调函数 - down
     */
    std::function<void()> on_swipe_down_;
    /**
     * @brief 手势回调函数 - tap
     */
    std::function<void()> on_tap_;
    /**
     * @brief 手势回调函数 - longPress
     */
    std::function<void()> on_long_press_;

    /**
     * @brief 触摸检测定时器回调
     */
    void onTouchDetectTimerCallback();

    /**
     * @brief 处理触摸事件
     * @param point 触摸点信息
     */
    void ProcessTouchEvent(const TouchPoint& point);

    /**
     * @brief 触摸手势回调分发函数
     */
    void OnTouchGestureDispatch(TouchGesture gesture, const TouchPoint& start, const TouchPoint& end);

    /**
     * @brief 检测手势
     * @param ended 是否触摸结束
     */
    TouchGesture DetectGesture(bool ended);

    /**
     * @brief 检测滑动手势
     * @param start_point 起始点
     * @param end_point 结束点
     * @return 检测到的手势
     */
    TouchGesture DetectSwipeGesture(const TouchPoint& start_point, const TouchPoint& end_point);

    /**
     * @brief 计算两点间距离
     * @param p1 点1
     * @param p2 点2
     * @return 距离
     */
    float CalculateDistance(const TouchPoint& p1, const TouchPoint& p2);

private:
	lv_indev_t* indev_;                    // 滑动对象
    
    // 触摸状态
    TouchPoint touch_start_;              // 触摸开始点
    TouchPoint touch_current_;            // 当前触摸点
    bool is_touching_;                    // 是否正在触摸
    bool gesture_detected_;               // 是否已检测到手势
    
    // 手势参数
    int16_t swipe_threshold_;             // 滑动阈值
    uint32_t long_press_time_;            // 长按时间阈值
    
    // 手势启用状态
    bool gestures_enabled_[static_cast<int>(TouchGesture::MAX)];            // 对应TouchGesture枚举的启用状态
    
    // 触摸检测定时器
    esp_timer_handle_t detect_timer_;

    // 是否启用
    bool enabled_ = false;
    static const char* TAG;
};

/**
 * @brief 手势名称转换函数
 * @param gesture 手势类型
 * @return 手势名称字符串
 */
const char* GetGestureName(TouchGesture gesture);

/**
 * @brief 创建滑动动画效果
 * @param obj 要动画的对象
 * @param direction 滑动方向
 * @param distance 滑动距离
 * @param duration 动画时长（毫秒）
 */
void CreateSwipeAnimation(lv_obj_t* obj, TouchGesture direction, int16_t distance, uint32_t duration);

/**
 * @brief 创建弹性滑动效果
 * @param obj 要动画的对象
 * @param direction 滑动方向
 * @param distance 滑动距离
 */
void CreateElasticSwipeEffect(lv_obj_t* obj, TouchGesture direction, int16_t distance);
