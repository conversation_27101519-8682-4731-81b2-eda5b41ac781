# 长按语音识别功能使用指南

## 功能概述

本项目已为 Waveshare ESP32-S3 Touch AMOLED 1.75" 开发板添加了长按按钮触发语音识别的功能，让您可以在非唤醒状态下直接进行语音转文字。

## 使用方法

### 1. 长按语音识别
- **操作**: 长按 Boot 按钮（GPIO0）
- **功能**: 开始语音识别录音
- **显示**: 屏幕会显示 "🎤 长按说话中... 松开结束录音"

### 2. 松开停止录音
- **操作**: 松开 Boot 按钮
- **功能**: 停止录音并开始语音识别
- **显示**: 屏幕会显示 "🎤 录音结束 正在识别..."

### 3. 其他按钮功能
- **单击**: 切换聊天状态（原有功能）
- **长按**: 重置WiFi配置
- **双击**: 切换AEC模式（如果启用）
- **三击**: 重启系统

## 技术实现

### 代码修改位置
文件: `main/boards/waveshare-s3-touch-amoled-1.75/esp32-s3-touch-amoled-1.75.cc`

### 关键功能
1. **按下检测**: `boot_button_.OnPressDown()` - 启动语音识别
2. **松开检测**: `boot_button_.OnPressUp()` - 停止语音识别
3. **视觉反馈**: 通过显示屏提供用户反馈
4. **电源管理**: 自动唤醒设备

### 语音处理流程
1. 用户长按按钮 → 启动 `Application::StartListening()`
2. 音频服务开始录音 → `AudioService::EnableVoiceProcessing(true)`
3. 音频数据通过OPUS编码 → 发送到服务器
4. 用户松开按钮 → 调用 `Application::StopListening()`
5. 服务器进行ASR处理 → 返回识别结果

## 优势

1. **无需唤醒词**: 直接通过按钮触发，避免误唤醒
2. **精确控制**: 用户可以精确控制录音的开始和结束时间
3. **即时反馈**: 屏幕显示当前状态，用户体验良好
4. **低功耗**: 只在需要时启动语音识别功能

## 注意事项

1. 确保设备已连接到网络（WiFi或4G）
2. 确保服务器端ASR服务正常运行
3. 长按时间建议超过0.5秒以避免误触
4. 录音过程中保持按钮按下状态

## 故障排除

### 问题1: 按钮无响应
- 检查GPIO0连接是否正常
- 确认设备已正确启动

### 问题2: 语音识别失败
- 检查网络连接状态
- 确认服务器端ASR服务运行正常
- 查看串口日志获取详细错误信息

### 问题3: 显示异常
- 重启设备
- 检查显示屏连接

## 扩展功能

如需为其他开发板添加类似功能，可参考以下步骤：

1. 找到对应开发板的按钮初始化代码
2. 添加 `OnPressDown` 和 `OnPressUp` 回调
3. 在回调中调用 `Application::StartListening()` 和 `StopListening()`
4. 添加适当的用户反馈（LED、显示屏等）

## 相关文件

- `main/application.cc` - 应用程序主逻辑
- `main/audio/audio_service.cc` - 音频服务
- `main/boards/waveshare-s3-touch-amoled-1.75/esp32-s3-touch-amoled-1.75.cc` - 开发板配置
