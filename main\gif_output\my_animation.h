#pragma once

#include "lvgl.h"
#include "display/display.h"

// my_animation GIF动画声明
// 总帧数: 11

// 声明所有帧
LV_IMAGE_DECLARE(loading_frame_001);
LV_IMAGE_DECLARE(loading_frame_002);
LV_IMAGE_DECLARE(loading_frame_003);
LV_IMAGE_DECLARE(loading_frame_004);
LV_IMAGE_DECLARE(loading_frame_005);
LV_IMAGE_DECLARE(loading_frame_006);
LV_IMAGE_DECLARE(loading_frame_007);
LV_IMAGE_DECLARE(loading_frame_008);
LV_IMAGE_DECLARE(loading_frame_009);
LV_IMAGE_DECLARE(loading_frame_010);
LV_IMAGE_DECLARE(loading_frame_011);

// 帧数组
extern const lv_image_dsc_t* loading_frames[11];

// 动画配置
#define MY_ANIMATION_FRAME_COUNT 11
#define MY_ANIMATION_FRAME_DURATION 100  // 毫秒

// 创建GIF动画的辅助函数
void create_my_animation_animation(Display* display);
void example_usage();
